package com.teyuntong.infra.basic.resource.client.popup.dto;

import lombok.Getter;

/**
 * 弹框模板类型
 */
@Getter
public enum PopupTypeEnum {
    注册后首次登陆app未身份认证(100, 1),
    注册后首次登陆app身份认证通过(100, 2),
    身份为试用后首次登录app(100, 3),
    车会员即将到期(100, 4),
    找货次卡即将到期(100, 5),
    货会员即将到期(100, 6),
    发货次卡即将到期(100, 7),
    购买车会员且支付成功(100, 8),
    购买货会员且支付成功(100, 9),
    未认证身份(100, 10),
    拨打电话权益即将到期提醒(100, 11),
    当日查看电话次数已满(100, 12),
    无拨打权益(100, 13),
    免费领试用会员(100, 14),
    无查看信用权益(100, 15),
    信用查询次数已用完(100, 16),
    无新货提醒权限(100, 17),
    无发货权益(100, 18),
    无回拨联系人权益(100, 19),
    身份认证中提示(100, 20),
    购买车货组合会员(100, 21),
    发货次卡到期提醒(100, 22),
    发货会员到期提醒(100, 23),
    找货次卡到期提醒(100, 24),
    找货会员到期提醒(100, 25),
    促活找货次卡用完提醒(100, 30),
    促活车辆认证提醒(100, 31),
    购买车会员抽奖活动提醒(100, 32),
    购买货会员抽奖活动提醒(100, 33),
    车会员履约联合活动提醒(100, 34),
    货会员履约联合活动提醒(100, 35),
    车次卡权益获取提醒(100, 41),
    车促活弹窗_明日继续赠送(100, 50),
    车促活弹窗_明日不赠送(100, 51),
    车促活弹窗1(100, 52),
    车促活弹窗2(100, 53),
    车促活分享弹窗(100, 54),
    卡券剩余3天到期(200, 1),
    注册后首次登陆app未实名认证(500, 1),
    未实名认证(500, 2),
    注册后首次登陆车app未实名认证(500, 3),
    注册后首次登陆货app未实名认证(500, 4),
    注册后首次登陆车app实名认证通过(500, 5),
    注册后首次登陆货app实名认证通过(500, 6),
    注册后首次登陆货app未实名认证New(500, 7),
    每月赠送发货权益(500, 8),
    获得发货权益(500, 9),
    货app首次注销提示(500, 10),
    货app第二次注销提示(500, 11),
    货app第三次注销提示(500, 12),
    注册后车主身份认证通过_新(500, 13),
    专车好货提醒(600, 1),
    老版本货站升级提示(700, 1),
    老版本车主升级提示(700, 2),
    车主版6140_6141_6142升级弹窗(700, 3),
    货站版网页强制升级弹窗(700, 4),//登录时,
    发货限制5分钟(800, 1),
    发货限制24小时(800, 2),
    发货权限已关闭(800, 3),
    您的电议发货次数已用尽(800, 4),
    发货时间段限制(800, 5),
    发货长期限制(800, 6),
    找货时间段限制(800, 7),
    找货长期限制(800, 8),
    找货时间段限制_有订单的处罚(800, 9),
    找货长期限制_有订单的处罚(800, 10),
    会员升级(900, 1),
    车货分离活动(900, 2),
    账号管制通知_拉黑(1000, 1),
    账号管制通知_订单(1000, 2),
    账号权益限制提醒_长期_有订单(1100, 1),
    账号权益限制提醒_长期_无订单(1100, 2),
    账号权益限制提醒_短期_有订单(1100, 3),
    账号权益限制提醒_短期_无订单(1100, 4),
    阻断提醒文案(1500, 1),
    阻断文案(1500, 2),
    未认证阻断文案(1500, 3),
    未拨打电话新用户弹窗提醒(100,60),
    已首活未首履有保障弹窗提醒(100,61),
    新用户生命周期注册弹窗(100,62),
    已首活未首履无保障弹窗提醒(100,63),
    已首活未首履有优惠券弹窗提醒(100,64),
    已首活未首履无优惠券弹窗提醒(100,65),
    新注册账号拨打豁免弹窗(10001,1),
    新注册账号拨打豁免弹窗2(10001,2);


    private int type1;
    private int type2;

    PopupTypeEnum(int type1, int type2) {
        this.type1 = type1;
        this.type2 = type2;
    }

    public static PopupTypeEnum getByName(String name) {
        for (PopupTypeEnum tEnum : values()) {
            if (tEnum.name().equals(name)) {
                return tEnum;
            }
        }
        return null;
    }
}
