package com.teyuntong.infra.basic.resource.client.tytabtest.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ABTestDto {

    /**
     * AB测试code集合
     */
    @NotNull(message = "AB测试code集合不能为空")
    @Size(min = 1, message = "AB测试code集合至少包含一个元素")
    private List<String> codeList;

    /**
     * 用户ID
     */
    private Long userId;

}