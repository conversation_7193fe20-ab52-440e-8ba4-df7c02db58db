package com.teyuntong.infra.basic.resource.client.popup.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 弹窗模板表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */
@Getter
@Setter
public class NoticePopupTemplVo {

    private Long id;

    /**
     * 业务类型 100:权益
     */
    private Integer type1;

    /**
     * 通知类型
     */
    private Integer type2;

    /**
     * 备注
     */
    private String remark;

    /**
     * 弹窗样式
     */
    private String style;

    /**
     * 标题
     */
    private String title;

    /**
     * 标题颜色
     */
    private String titleColor;

    /**
     * 主内容
     */
    private String masterContent;

    /**
     * 主内容文字颜色
     */
    private String masterContentColor;

    /**
     * 主按钮
     */
    private String leftButtonContent;

    /**
     * 主按钮连接
     */
    private String leftButtonLink;

    /**
     * 左按钮类型
     */
    private String leftButtonType;

    /**
     * 左按钮颜色
     */
    private String leftButtonBackcolor;

    /**
     * 左按钮文字颜色
     */
    private String leftButtonContentColor;

    /**
     * 次按钮
     */
    private String rightButtonContent;

    /**
     * 右按钮类型
     */
    private String rightButtonType;

    /**
     * 次按钮连接
     */
    private String rightButtonLink;

    /**
     * 右按钮颜色
     */
    private String rightButtonBackcolor;

    /**
     * 右按钮文字颜色
     */
    private String rightButtonContentColor;

    /**
     * 引导内容
     */
    private String guideContent;

    /**
     * 引导内容类型
     */
    private String guideContentType;

    /**
     * 引导内容颜色
     */
    private String guideContentColor;

    /**
     * 引导语内容背景颜色
     */
    private String guideContentBackcolor;

    /**
     * 引导语连接
     */
    private String guideContentLink;

    /**
     * 引导按钮
     */
    private String guideButtonContent;

    /**
     * 引导按钮文字颜色
     */
    private String guideButtonContentColor;

    /**
     * 引导按钮背景颜色
     */
    private String guideButtonBackcolor;

    /**
     * 引导按钮类型
     */
    private String guideButtonType;

    /**
     * 引导按钮连接
     */
    private String guideButtonLink;

    /**
     * 图片地址
     */
    private String pictureAddr;
    

    private Date ctime;
}
