package com.teyuntong.infra.basic.resource.client.tytcity.service;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025/02/05 18:22
 */
public interface CityNewRpcService {

    @GetMapping(value = "/rpc/tytCityNew/queryMapCodeByAddress")
    String queryMapCodeByAddress(@RequestParam("address") String address);
}
