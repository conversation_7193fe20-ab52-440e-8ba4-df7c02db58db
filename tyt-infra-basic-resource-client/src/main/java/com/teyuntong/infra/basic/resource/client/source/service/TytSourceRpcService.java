package com.teyuntong.infra.basic.resource.client.source.service;


import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 字典相关RPC
 */
public interface TytSourceRpcService {

    @PostMapping(value = "/rpc/source/getListByGroupCode")
    List<TytSourceVO> getListByGroupCode(@RequestParam("groupCode") String groupCode);

    /**
     * 根据分组代码和属性值获取字典详情
     *
     * @param groupCode
     * @param value
     * @return
     */
    @PostMapping(value = "/rpc/source/getByGroupCodeAndValue")
    TytSourceVO getByGroupCodeAndValue(@RequestParam("groupCode") String groupCode, @RequestParam("value") String value);

    /**
     * 根据根据分组代码和属性值，子类属性值获取字典详情
     *
     * @param groupCode
     * @param value
     * @param subValue
     * @return
     */
    @PostMapping(value = "/rpc/source/getByGroupCodeValueSubValue")
    TytSourceVO getByGroupCodeValueSubValue(@RequestParam("groupCode") String groupCode,
                                            @RequestParam("value") String value,
                                            @RequestParam("subValue") String subValue);


    @PostMapping(value = "/rpc/source/getListByGroupCodeList")
    List<TytSourceVO> getListByGroupCodeList(@RequestParam("groupCodeList") List<String> groupCodeList);
}
