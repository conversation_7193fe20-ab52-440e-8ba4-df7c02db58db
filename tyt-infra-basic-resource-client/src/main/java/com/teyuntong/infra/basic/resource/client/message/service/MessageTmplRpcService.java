package com.teyuntong.infra.basic.resource.client.message.service;


import com.teyuntong.infra.basic.resource.client.message.vo.MessageTmplRpcVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 短信模板RPC服务类
 *
 * <AUTHOR>
 * @since 2024-11-15 17:46:59
 */
public interface MessageTmplRpcService {

    /**
     * 根据KEY获取短信模板
     */
    @GetMapping(value = "/rpc/sms/tmpl/get")
    MessageTmplRpcVO getTmplByKey(@RequestParam("key") String key);
}
