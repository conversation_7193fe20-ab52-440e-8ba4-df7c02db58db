package com.teyuntong.infra.basic.resource.client.message.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class MessageTmplRpcVO implements Serializable {

    private Long id;

    /**
     * 模版类型 0是sms 1中列表消息
     */
    private String type;

    /**
     * 内容类型1string 2 json
     */
    private String contentType;

    /**
     * 模版key
     */
    private String tmplKey;

    /**
     * 模版内容 如果是列表消息 保存json
     */
    private String content;

    /**
     * 是否有效0是有效1是无效
     */
    private String status;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 备注
     */
    private String remark;
}
