package com.teyuntong.infra.basic.resource.client.universalword.service;

import com.teyuntong.infra.basic.resource.client.universalword.vo.UniversalWordConfigInfoVO;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

public interface UniversalWordConfigRpcService {


    /**
     * 查询车或者货的全部通用文案
     *
     * @param type 1:车，2:货，3：pc
     * @return UniversalWordConfigInfoVO
     */
    @PostMapping(value = "/rpc/universalWord/getAllUniversalWordConfigInfoListByType")
    List<UniversalWordConfigInfoVO> getAllUniversalWordConfigInfoListByType(Integer type);

    /**
     * 根据 code 集合获取通用文案配置
     * @param codes 多个code间用逗号分隔拼接而成的字符串
     * @return UniversalWordConfigInfoVO
     */
    @PostMapping(value = "/rpc/universalWord/getAllUniversalWordConfigInfoListByCodeList")
    List<UniversalWordConfigInfoVO> getAllUniversalWordConfigInfoListByCodeList(List<String> codes);

}
