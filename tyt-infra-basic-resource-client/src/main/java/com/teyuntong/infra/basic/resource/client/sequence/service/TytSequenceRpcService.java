package com.teyuntong.infra.basic.resource.client.sequence.service;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025/2/18 15:41
 */
public interface TytSequenceRpcService {

    /**
     * 发号器发号
     *
     * @param name
     * @return
     */
    @GetMapping(value = "/rpc/sequence/get")
    String getSequence(@RequestParam("name") String name);


}
