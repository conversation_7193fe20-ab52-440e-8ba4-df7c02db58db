package com.teyuntong.infra.basic.resource.client.tytcity.vo;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 地图距离字典
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-21
 */
@Data
public class MapDictVo {

    private Long id;

    /**
     * 两点唯一值key
     */
    private String mapKey;

    /**
     * 出发地省
     */
    private String startProvinc;

    /**
     * 出发地市
     */
    private String startCity;

    /**
     * 出发地区
     */
    private String startArea;

    /**
     * 目的地省
     */
    private String destProvinc;

    /**
     * 目的地市
     */
    private String destCity;

    /**
     * 目的地区
     */
    private String destArea;

    /**
     * 出发地坐标x
     */
    private Integer startCoordX;

    /**
     * 出发地坐标y
     */
    private Integer startCoordY;

    /**
     * 出发地经度
     */
    private Integer startLongitude;

    /**
     * 出发地纬度
     */
    private Integer startLatitude;

    /**
     * 目的地坐标x
     */
    private Integer destCoordX;

    /**
     * 目的地坐标y
     */
    private Integer destCoordY;

    /**
     * 目的地经度
     */
    private Integer destLongitude;

    /**
     * 目的地纬度
     */
    private Integer destLatitude;

    /**
     * android两点距离分米
     */
    private Integer distance;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * IOS 距离分米
     */
    private Integer iosDistance;

    /**
     * 直线距离
     */
    private Integer straightDistance;
}
