package com.teyuntong.infra.basic.resource.client.popup.vo;


import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 弹窗通知表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */
@Data
public class NoticePopupVo {

    private Long id;

    /**
     * 业务类型
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 信息id
     */
    private String msgId;

    /**
     * 弹窗替换内容，逗号分隔
     */
    private String content;

    /**
     * 模板id
     */
    private Long templId;

    /**
     * 通知人id(0系统)
     */
    private Long productionId;

    /**
     * 被通知人id
     */
    private Long receiveId;

    /**
     * 通知接受时间
     */
    private Date receiveTime;

    /**
     * 通知状态1.未发送 2.已发送
     */
    private Integer receiveStatus;


    private Date ctime;

    /**
     * 原弹窗 0不弹 1弹
     */
    private Integer originPopup;

    /**
     * 车主版弹窗 0不弹 1弹
     */
    private Integer carPopup;

    /**
     * 货主版弹窗 0不弹 1弹
     */
    private Integer goodsPopup;
}
