package com.teyuntong.infra.basic.resource.client.modify.service;


import com.teyuntong.infra.basic.resource.client.modify.vo.ModifyResourceOrConfigVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 修改开关、公共资源、ab测试发送钉钉通知消息RPC
 */
public interface ModifyResourceOrConfigRpcService {

    @PostMapping(value = "/rpc/modifyResourceOrConfig/dingMessage")
    void dingMessage(@RequestBody ModifyResourceOrConfigVO modifyResourceOrConfigVO);

}
