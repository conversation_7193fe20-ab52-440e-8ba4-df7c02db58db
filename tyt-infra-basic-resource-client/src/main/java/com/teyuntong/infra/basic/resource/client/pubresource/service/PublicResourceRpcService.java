package com.teyuntong.infra.basic.resource.client.pubresource.service;


import com.teyuntong.infra.basic.resource.client.pubresource.vo.PublicResourceVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 公共资源RPC
 */
public interface PublicResourceRpcService {
    /**
     * 单个查询公共资源
     *
     * @param paramName
     * @return
     */
    @GetMapping(value = "/rpc/pubResource/getByName")
    PublicResourceVO getByName(@RequestParam("paramName") String paramName);

    /**
     * 批量查询公共资源
     *
     * @param paramNameList
     * @return
     */
    @GetMapping(value = "/rpc/pubResource/getByNameList")
    List<PublicResourceVO> getByNameList(@RequestParam("paramNameList") List<String> paramNameList);

    /**
     * 查询所有公共资源
     *
     * @return
     */
    @GetMapping(value = "/rpc/pubResource/getAll")
    List<PublicResourceVO> getAll();
}
