package com.teyuntong.infra.basic.resource.client.tytconfig.service;


import com.teyuntong.infra.basic.resource.client.tytconfig.dto.ConfigRpcDto;
import com.teyuntong.infra.basic.resource.client.tytconfig.vo.ConfigRpcVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @author: helian
 * @since: 2023/12/26 14:40
 */
public interface ConfigRpcService {
    /**
     * 根据名称获取配置信息
     *
     * @param name
     * @return
     */
    @GetMapping(value = "/rpc/config/getByName")
    ConfigRpcVo getByName(@RequestParam("name") String name);

    /**
     * 根据name获取value的number
     *
     * @param name
     * @return
     */
    @GetMapping(value = "/rpc/config/getIntValue")
    Integer getIntValue(@RequestParam("name") String name);

    /**
     * 根据name获取value的number,不存在返回默认
     *
     * @param name
     * @return
     */
    @GetMapping(value = "/rpc/config/getIntValue/default")
    Integer getIntValue(@RequestParam("name") String name, @RequestParam("defaultValue") int defaultValue);

    /**
     * 根据name获取value的string
     *
     * @param name
     * @return
     */
    @GetMapping(value = "/rpc/config/getStringValue")
    String getStringValue(@RequestParam("name") String name);

    /**
     * 根据name获取value的string,不存在返回默认
     *
     * @param name
     * @return
     */
    @GetMapping(value = "/rpc/config/getStringValue/default")
    String getStringValue(@RequestParam("name") String name, @RequestParam("defaultValue") String defaultValue);

    /**
     * 新增
     *
     * @param configRpcReq
     * @return
     */
    @PostMapping(value = "/rpc/config/value/save", consumes = "application/json")
    boolean save(@RequestBody @Validated ConfigRpcDto configRpcReq);


    /**
     * 更新value的值
     *
     * @param configRpcReq
     * @return
     */
    @PostMapping(value = "/rpc/config/value/update", consumes = "application/json")
    boolean update(@RequestBody @Validated ConfigRpcDto configRpcReq);


    /**
     * 删除配置
     *
     * @param name
     * @return
     */
    @PostMapping(value = "/rpc/config/deleteByName")
    boolean deleteByName(@RequestParam("name") String name);

    /**
     * 批量查询配置信息
     *
     * @return
     */
    @GetMapping(value = "/rpc/config/getByNameList")
    Map<String, String> getByNameList(@RequestParam("nameList") List<String> nameList);

}
