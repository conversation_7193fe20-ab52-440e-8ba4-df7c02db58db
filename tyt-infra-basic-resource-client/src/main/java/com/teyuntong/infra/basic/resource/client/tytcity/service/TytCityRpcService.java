package com.teyuntong.infra.basic.resource.client.tytcity.service;


import com.teyuntong.infra.basic.resource.client.tytcity.dto.TytCityDto;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.TytCityVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: zhanglubin
 * @since: 2024/7/18 14:40
 */
public interface TytCityRpcService {

    @PostMapping(value = "/rpc/tytCity/getCityDataByCondition")
    TytCityVo getCityDataByCondition(@RequestBody TytCityDto tytCityDto);

    @GetMapping(value = "/rpc/tytCity/getByAddress")
    TytCityVo getByAddress(@RequestParam("address") String address);

    @GetMapping(value = "/rpc/tytCity/getRegxByName")
    TytCityVo getRegxByName(@RequestParam("cityName") String cityName, @RequestParam("areaName") String areaName, @RequestParam(value = "level", required = false, defaultValue = "3") String level);

    @GetMapping(value = "/rpc/tytCity/getShortCityName")
    TytCityVo getShortCityName(@RequestParam("cityName") String cityName);

    @GetMapping(value = "/rpc/tytCity/getCityByXY")
    TytCityVo getCityByXY(@RequestParam("px") String px, @RequestParam("py") String py, @RequestParam(value = "level", required = false, defaultValue = "3") String level);


}
