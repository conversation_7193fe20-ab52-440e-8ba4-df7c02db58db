package com.teyuntong.infra.basic.resource.client.tytabtest.service;


import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.AbTestConfigVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: zhanglubin
 * @since: 2024/7/18 14:40
 */
public interface ABTestRpcService {

    /**
     * 根据AB测试Code集合和用户ID获取这个用户这些AB测试的Type值集合，如果AB测试不存在则对应的Type返回0
     *
     * @param abTestReq the ABTestDto object containing the AB test code list and user ID
     * @return a list of ABTestVo objects
     */
    @PostMapping(value = "/rpc/abTest/getUserTypeList")
    List<ABTestVo> getUserTypeList(@RequestBody ABTestDto abTestReq);

    /**
     * 根据code查询abtest配置详情
     *
     * @param code
     * @return
     */
    @GetMapping("/rpc/abTest/getAbTestConfig")
    AbTestConfigVO getAbTestConfig(@RequestParam("code") String code);

    /**
     * 根据AB测试Code和用户ID获取这个用户这个AB测试的Type值，如果AB测试不存在则Type返回0
     *
     * @param code   the AB test code
     * @param userId the user ID
     * @return the user type, or null if code is blank or userId is null
     */
    @GetMapping(value = "/rpc/abTest/getUserType")
    Integer getUserType(@RequestParam("code")String code, @RequestParam("userId")Long userId);

    /**
     * 更新userType
     */
    @GetMapping(value = "/rpc/abTest/updateUserType")
    Integer updateUserType(@RequestParam("code") String code, @RequestParam("userId") Long userId, @RequestParam("userType") Integer userType);
}
