package com.teyuntong.infra.basic.resource.client.tytcity.service;

import com.teyuntong.infra.basic.resource.client.tytcity.dto.MapDictDto;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.MapDictVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2025/2/21 16:18
 */
public interface MapDictRpcService {

    @PostMapping(value = "/rpc/mapDict/getMapDict")
    MapDictVo getMapDict(@RequestBody MapDictDto mapDIctDto);
}
