package com.teyuntong.infra.basic.resource.client.tytconfig.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ConfigRpcDto {

    /**
     * 键
     */
    @NotBlank(message = "名称不能为空")
    private String name;
    /**
     * 值
     */
    private String value;

    /**
     * 单位
     */
    private String unit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 配置类型 0服务器 1PC 2APP
     */
    private String parentType;

    /**
     * 业务类型-1：废弃配置 0：其他配置 1：货源置顶配置 2：日志开关配置  3：升级配置 4：特运通信息配置 5：财务信息配置 6：实名认证配置 7：客户端升级配置 8：信息费配置 9：货物标准化配置 10：后台导出配置 11：PC找货配置 12：PC发货配置 13：登录配置 14：邀请好友推广配置 15：轮播图配置 16：APP找货配置 17：发货配置 18：推送配置 19：身份认证配置 20：精准货源推荐配置 21：BI报表配置
     */
    private String subType;


}
