package com.teyuntong.infra.basic.resource.client.popup.service;


import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 弹窗模板表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */
public interface NoticePopupTemplRpcService {

    /**
     * 根据弹窗类型获取用户
     *
     * @param popupTypeEnum
     * @return
     */
    @PostMapping(value = "/rpc/popup/temple/getByType")
    NoticePopupTemplVo getByType(@RequestBody PopupTypeEnum popupTypeEnum);


}
