package com.teyuntong.infra.basic.resource.client.source.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TytSourceVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 分组代码
     */
    private String groupCode;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 属性值
     */
    private String value;

    /**
     * 属性名称
     */
    private String name;

    /**
     * 属性简称
     */
    private String shortName;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 父级编码
     */
    private String parent;

    /**
     * 0是正常1是无效
     */
    private Integer status;

    /**
     * 字典状态0是正常1是无效
     */
    private Integer dictStatus;
}