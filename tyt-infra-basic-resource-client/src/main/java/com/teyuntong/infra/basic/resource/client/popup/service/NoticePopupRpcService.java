package com.teyuntong.infra.basic.resource.client.popup.service;


import com.teyuntong.infra.basic.resource.client.popup.dto.NoticePopupDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 弹窗通知表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */
public interface NoticePopupRpcService {

    /**
     * 保存弹窗通知表
     *
     * @param noticePopupDTO
     */
    @PostMapping("/rpc/notice/popup/save")
    void saveNoticePopup(@RequestBody NoticePopupDTO noticePopupDTO);

}
