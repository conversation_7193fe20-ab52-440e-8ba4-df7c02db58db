package com.teyuntong.infra.basic.resource.adapter.navigation;

import com.teyuntong.infra.basic.resource.service.remote.outer.DistanceRemoteService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.distance.dto.PoiRpcDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 导航服务
 *
 * <AUTHOR>
 * @since 2025/09/04 09:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/navigation")
public class NavigationController {

    private final DistanceRemoteService distanceRemoteService;

    /**
     * 关键字搜索poi
     */
    @PostMapping("/poi")
    public WebResult<String> poi(@RequestBody PoiRpcDTO poiRpcDTO) {
        return WebResult.success(distanceRemoteService.poi(poiRpcDTO));
    }

}
