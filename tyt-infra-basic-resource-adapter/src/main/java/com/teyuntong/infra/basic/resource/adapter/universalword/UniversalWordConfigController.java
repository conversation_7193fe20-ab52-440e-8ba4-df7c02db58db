package com.teyuntong.infra.basic.resource.adapter.universalword;

import com.teyuntong.infra.basic.resource.client.universalword.service.UniversalWordConfigRpcService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 * 通用文案配置
 * 通用文案配置
 */
@RestController
@RequestMapping("/universalWordConfig")
public class UniversalWordConfigController {

    @Autowired
    private UniversalWordConfigRpcService universalWordConfigRpcService;

    /**
     * 查询车或货的所有通用文案配置
     *
     * @param type 1:车，2:货
     * @return ResultMsgBean List UniversalWordConfigInfo
     */
    @GetMapping("/getAllUniversalWordConfigInfoListByType")
    public WebResult getUniversalWordConfigInfoList(@RequestParam("type") Integer type) {
        if (type == null || (type != 1 && type != 2 && type != 3)) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        return WebResult.success(universalWordConfigRpcService.getAllUniversalWordConfigInfoListByType(type));
    }

    /**
     * 通过code集合查询通用文案配置
     *
     * @param codes 多个code间用逗号分隔拼接而成的字符串
     * @return ResultMsgBean List UniversalWordConfigInfo
     */
    @PostMapping("/getAllUniversalWordConfigInfoListByCodeList")
    public WebResult getAllUniversalWordConfigInfoListByCodeList(String codes) {
        if (StringUtils.isBlank(codes)) {
            return WebResult.error(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        List<String> codeList = Arrays.stream(codes.trim().split(COMMA)).collect(Collectors.toList());
        codeList = codeList.stream().map(String::trim).collect(Collectors.toList());
        return WebResult.success(universalWordConfigRpcService.getAllUniversalWordConfigInfoListByCodeList(codeList));
    }

}
