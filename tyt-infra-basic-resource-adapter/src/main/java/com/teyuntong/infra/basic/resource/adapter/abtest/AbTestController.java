package com.teyuntong.infra.basic.resource.adapter.abtest;

import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.service.ABTestRpcService;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/14 17:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/ab/test")
public class AbTestController {

    private final ABTestRpcService abTestRpcService;

    /**
     * 获取所有公共资源
     *
     * @param abTestDto
     * @return
     */
    @PostMapping("/getUserTypeList")
    public WebResult<List<ABTestVo>> getUserTypeList(@RequestBody @Validated ABTestDto abTestDto) {
        if (abTestDto.getUserId() == null) {
            abTestDto.setUserId(LoginHelper.getRequiredLoginUser().getUserId());
        }
        return WebResult.success(abTestRpcService.getUserTypeList(abTestDto));
    }

    /**
     * 批量根据名称获取公共资源
     *
     * @param code
     * @param userId
     * @return
     */
    @GetMapping("/getUserType")
    public WebResult<Integer> getByNameList(@RequestParam("code") String code, @RequestParam(value = "userId", required = false) Long userId) {
        if (userId == null) {
            userId = LoginHelper.getRequiredLoginUser().getUserId();
        }
        return WebResult.success(abTestRpcService.getUserType(code, userId));
    }

}
