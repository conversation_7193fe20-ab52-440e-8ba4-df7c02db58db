package com.teyuntong.infra.basic.resource.adapter.source;

import com.teyuntong.infra.basic.resource.client.source.service.TytSourceRpcService;
import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-3-19 17:44:56
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/source")
public class SourceController {

    private final TytSourceRpcService tytSourceRpcService;

    /**
     * 根据code查询字典信息
     */
    @GetMapping("/getByCode")
    public WebResult<List<TytSourceVO>> getByCode(@RequestParam("groupCode") String groupCode) {
        return WebResult.success(tytSourceRpcService.getListByGroupCode(groupCode));
    }
}
