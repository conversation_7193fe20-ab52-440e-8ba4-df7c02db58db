package com.teyuntong.infra.basic.resource.adapter.pubresource;

import com.teyuntong.infra.basic.resource.adapter.pubresource.dto.PublicResourceDTO;
import com.teyuntong.infra.basic.resource.client.pubresource.service.PublicResourceRpcService;
import com.teyuntong.infra.basic.resource.client.pubresource.vo.PublicResourceVO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

import static cn.hutool.core.text.StrPool.COMMA;

/**
 * <AUTHOR>
 * @since 2024/12/14 17:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/public/resource")
public class PublicResourceController {

    private static final Logger log = LoggerFactory.getLogger(PublicResourceController.class);
    private final PublicResourceRpcService publicResourceRpcService;

    /**
     * 获取所有公共资源
     *
     * @return
     */
    @GetMapping("/getAll")
    public WebResult<List<PublicResourceVO>> getAll() {
        return WebResult.success(publicResourceRpcService.getAll());
    }


    /**
     * 批量根据名称获取公共资源
     *
     * @param paramName
     * @return
     */
    @GetMapping("/getByName")
    public WebResult<PublicResourceVO> getByName(@RequestParam("paramName") String paramName) {
        return WebResult.success(publicResourceRpcService.getByName(paramName.trim()));
    }

    /**
     * 批量根据名称获取公共资源
     *
     * @param paramNameList
     * @return
     */
    @PostMapping("/getByNameList")
    public WebResult<List<PublicResourceVO>> getByNameList(@RequestParam("paramNameList") String paramNameList) {
        if (StringUtils.isBlank(paramNameList)) {
            throw new BusinessException(CommonErrorCode.ERROR_PARAMETER_LACK);
        }
        List<String> list = Arrays.stream(paramNameList.split(COMMA)).map(String::trim).toList();
        return WebResult.success(publicResourceRpcService.getByNameList(list));
    }

    /**
     * 批量根据名称获取公共资源
     *
     * @param publicResourceDTO
     * @return
     */
    @PostMapping("/getByNameListForJson")
    public WebResult<List<PublicResourceVO>> getByNameListForJson(@RequestBody @Validated PublicResourceDTO publicResourceDTO) {

        return WebResult.success(publicResourceRpcService.getByNameList(publicResourceDTO.getParamNameList()));
    }


}
