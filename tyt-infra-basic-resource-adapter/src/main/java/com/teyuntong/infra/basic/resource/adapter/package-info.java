/**
 * adapter 模块, 主要是定义对外部提供的接口和返回实体, 包目录和结构是灵活的，可以按照自己的需要增加/删除。
 * <p>
 * ├─adapter                            -- adapter模块, 负责把server的数据转换成各端需要的数据的格式
 * │  ├─web                               -- 处理和转换页面请求的controller
 * │  ├─app                               -- 处理和转换app页面请求的controller
 * │  ├─miniapp                          -- 处理和转换小程序页面请求的controller
 */

package com.teyuntong.infra.basic.resource.adapter;