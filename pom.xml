<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>


    <parent>
        <groupId>com.teyuntong</groupId>
        <artifactId>tyt-dependencies-spring-cloud</artifactId>
        <version>2021.0.9.0-********-1.1.0</version>
    </parent>

    <artifactId>tyt-infra-basic-resource</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>tyt-infra-basic-resource</name>
    <description>基础资源服务</description>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <tyt-infra-common-bom.version>2.1.3-SNAPSHOT</tyt-infra-common-bom.version>
        <tyt-outer-service-client.version>1.0.1-SNAPSHOT</tyt-outer-service-client.version>
        <!-- ==================== sonar ==================== -->
        <tyt-p3c-pmd.version>1.0.0</tyt-p3c-pmd.version>
        <sonar.projectKey>${project.artifactId}</sonar.projectKey>
        <sonar.projectName>${project.artifactId}</sonar.projectName>
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>
        <sonar.projectVersion>1.0</sonar.projectVersion>
        <sonar.inclusions>**/com/teyuntong/**/*.java,**/com/teyuntong/**/*.kt</sonar.inclusions>
        <sonar.java.binaries>target</sonar.java.binaries>
        <sonar.host.url>http://************:9000</sonar.host.url>
        <sonar.login>****************************************</sonar.login>

        <!-- JaCoCo Configuration for Sonar -->
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.coverage.jacoco.xmlReportPaths>${project.basedir}/target/site/jacoco/jacoco.xml,${project.basedir}/target/site/jacoco-aggregate/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
        <sonar.language>java</sonar.language>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-bom</artifactId>
                <version>${tyt-infra-common-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-outer-export-service-client</artifactId>
                <version>${tyt-outer-service-client.version}</version>
            </dependency>
        </dependencies>


    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <!-- 脚手架插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>

            <!-- sonar插件 -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>

            <!-- JaCoCo plugin for code coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.10</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>tyt-infra-basic-resource-adapter</module>
        <module>tyt-infra-basic-resource-client</module>
        <module>tyt-infra-basic-resource-mybatis-generator</module>
        <module>tyt-infra-basic-resource-schedule</module>
        <module>tyt-infra-basic-resource-service</module>
        <module>tyt-infra-basic-resource-starter</module>
    </modules>
</project>