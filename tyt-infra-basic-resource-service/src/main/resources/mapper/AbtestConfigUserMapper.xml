<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.mapper.AbtestConfigUserMapper">

    <select id="selectByABTestIdAndUserId"
            resultType="com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity.AbtestConfigUserDO">
        select *
        from tyt_abtest_config_user
        where abtest_id = #{abTestId}
          and user_id = #{userId}
    </select>

</mapper>
