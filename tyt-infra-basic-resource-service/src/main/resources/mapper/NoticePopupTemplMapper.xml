<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.mapper.NoticePopupTemplMapper">

    <select id="getByType"
            resultType="com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupTemplDO">
        select *
        from tyt_notice_popup_templ
        where type1 = #{type1}
          and type2 = #{type2}
          and status = 1
        order by id desc limit 1

    </select>
</mapper>
