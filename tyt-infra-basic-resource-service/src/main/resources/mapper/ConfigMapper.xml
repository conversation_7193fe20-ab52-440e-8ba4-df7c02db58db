<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.mapper.ConfigMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, value, unit, create_time, update_time, remark, parent_type, sub_type
    </sql>

    <delete id="deleteByName">
        delete
        from tyt_config
        where name = #{name}
    </delete>
    <select id="selectByName" resultType="com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.entity.ConfigDO">
        select
        <include refid="Base_Column_List"/>
        from tyt_config
        where name = #{name}
    </select>
    <select id="selectAll" resultType="com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.entity.ConfigDO">
        select
        <include refid="Base_Column_List"/>
        from tyt_config
    </select>

</mapper>
