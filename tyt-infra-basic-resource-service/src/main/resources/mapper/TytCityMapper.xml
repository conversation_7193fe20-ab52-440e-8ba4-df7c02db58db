<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.mapper.TytCityMapper">


    <select id="getCityDataByCondition"
            resultType="com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO">
        select *
        from tyt_city where 1 = 1
        <if test=" tytCityDto.provinceName != null and tytCityDto.provinceName != ''">
            and province_name = #{tytCityDto.provinceName}
        </if>
        <if test=" tytCityDto.cityName != null and tytCityDto.cityName != ''">
            and city_name = #{tytCityDto.cityName}
        </if>
        <if test=" tytCityDto.areaName != null and tytCityDto.areaName != ''">
            and area_name = #{tytCityDto.areaName}
        </if>
        <if test=" tytCityDto.mapCityName != null and tytCityDto.mapCityName != ''">
            and map_city_name = #{tytCityDto.mapCityName}
        </if>
        <if test=" tytCityDto.level != null and tytCityDto.level != ''">
            and level = #{tytCityDto.level}
        </if>
        limit 1
    </select>

    <select id="getByAddress"
            resultType="com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO">
        select *
        from tyt_city
        where
        <choose>
            <when test="isMunicipality">
                concat(city_name, area_name) = #{address}
            </when>
            <otherwise>
                concat(province_name, city_name, area_name) = #{address}
            </otherwise>
        </choose>
        limit 1
    </select>
    <select id="getRegxByName"
            resultType="com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO">
        select *
        from tyt_city
        where level = #{level}
        <if test="cityName != null and cityName != ''">
            and (city_name = #{cityName} or map_city_name = #{cityName})
        </if>
        <if test="areaName != null and areaName != ''">
            and (area_name = #{areaName} or map_area_name = #{areaName})
        </if>
        limit 1
    </select>
    <select id="getShortCityName"
            resultType="com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO">
        select *
        from tyt_city
        where (map_city_name = #{cityName} or city_name = #{cityName})
          and level = 2
        limit 1
    </select>
    <select id="getCityByXY"
            resultType="com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO">
        select *
        from tyt_city
        where level = #{level}
          and px = #{px}
          and py = #{py}
        limit 1
    </select>

</mapper>