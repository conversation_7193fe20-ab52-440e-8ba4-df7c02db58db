<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.mapper.CityNewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.CityNewDO">
        <result column="id" property="id" />
        <result column="area_code" property="areaCode" />
        <result column="parent_area_code" property="parentAreaCode" />
        <result column="level" property="level" />
        <result column="area_name" property="areaName" />
        <result column="map_area_name" property="mapAreaName" />
        <result column="map_code" property="mapCode" />
        <result column="city_name" property="cityName" />
        <result column="map_city_name" property="mapCityName" />
        <result column="province_name" property="provinceName" />
        <result column="map_province_name" property="mapProvinceName" />
        <result column="rf" property="rf" />
        <result column="px" property="px" />
        <result column="py" property="py" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="short_name" property="shortName" />
        <result column="standard_name" property="standardName" />
        <result column="map_longitude" property="mapLongitude" />
        <result column="map_latitude" property="mapLatitude" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, area_code, parent_area_code, level, area_name, map_area_name, map_code, city_name, map_city_name, province_name, map_province_name, rf, px, py, longitude, latitude, short_name, standard_name, map_longitude, map_latitude, create_time
    </sql>

</mapper>
