<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.mapper.AbtestConfigMapper">

    <select id="selectEnableABTestByCodeList"
            resultType="com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity.AbtestConfigDO">
        select * from tyt_abtest_config where code in
        <foreach collection="codeList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and enable = 1
    </select>
	<select id="selectByCode"
	        resultType="com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity.AbtestConfigDO">
		select *
		from tyt_abtest_config
		where code = #{code} and enable = 1
	</select>

</mapper>
