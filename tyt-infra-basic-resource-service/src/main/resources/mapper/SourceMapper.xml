<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.source.mybatis.mapper.SourceMapper">


    <select id="getListByGroupCode"  resultType="com.teyuntong.infra.basic.resource.service.biz.source.mybatis.entity.SourceDO">
        select *
        from tyt_source
        where group_code = #{groupCode}
    </select>
    <select id="getListByGroupCodeList"
            resultType="com.teyuntong.infra.basic.resource.service.biz.source.mybatis.entity.SourceDO">
        select *
        from tyt_source
        where group_code in
        <foreach collection="groupCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

	<select id="getByGroupCodeAndValue"
	        resultType="com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO">
        select *
        from tyt_source
        where group_code = #{groupCode} and value = #{value} and status = 0 and dict_status = 0
    </select>

    <select id="getSubGroupCode" resultType="java.lang.String">
        select group_code
        from tyt_source
        where parent = #{parent} limit 1
    </select>


</mapper>
