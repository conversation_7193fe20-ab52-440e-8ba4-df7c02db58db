<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.message.mybatis.mapper.MessageTmplMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.basic.resource.service.biz.message.mybatis.entity.MessageTmplDO">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="content_type" property="contentType" />
        <result column="tmpl_key" property="tmplKey" />
        <result column="content" property="content" />
        <result column="status" property="status" />
        <result column="ctime" property="ctime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, content_type, tmpl_key, content, status, ctime, remark
    </sql>

    <select id="getByKey" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from tyt_message_tmpl
        where type = 0
          and tmpl_key = #{key}
          and status = 0
        limit 1
    </select>

</mapper>
