<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.mapper.PublicResourceMapper">


    <select id="getByName"
            resultType="com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity.PublicResourceDO">
        select name, value
        from tyt_public_resource
        where name = #{name}
    </select>
    <select id="getByNameList"
            resultType="com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity.PublicResourceDO">
        select name,value
        from tyt_public_resource
        where name in
        <foreach collection="nameList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getAll"
            resultType="com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity.PublicResourceDO">
        select name, value
        from tyt_public_resource
    </select>
</mapper>
