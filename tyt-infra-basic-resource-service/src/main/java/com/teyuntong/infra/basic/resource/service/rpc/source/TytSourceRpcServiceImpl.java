package com.teyuntong.infra.basic.resource.service.rpc.source;


import cn.hutool.core.bean.BeanUtil;
import com.teyuntong.infra.basic.resource.client.source.service.TytSourceRpcService;
import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import com.teyuntong.infra.basic.resource.service.biz.source.mybatis.entity.SourceDO;
import com.teyuntong.infra.basic.resource.service.biz.source.service.SourceService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 短信模板RPC服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-15 17:46:59
 */
@RestController
@RequiredArgsConstructor
public class TytSourceRpcServiceImpl implements TytSourceRpcService {

    private final SourceService sourceService;

    @Override
    public List<TytSourceVO> getListByGroupCode(String groupCode) {
        return BeanUtil.copyToList(sourceService.getListByGroupCode(groupCode), TytSourceVO.class);
    }

    @Override
    public TytSourceVO getByGroupCodeAndValue(String groupCode, String value) {
        if (StringUtils.isAnyBlank(groupCode, value)) {
            return null;
        }
        return sourceService.getByGroupCodeAndValue(groupCode, value);
    }

    @Override
    public TytSourceVO getByGroupCodeValueSubValue(String groupCode, String value, String subValue) {
        if (StringUtils.isAnyBlank(groupCode, value, subValue)) {
            return null;
        }
        return sourceService.getByGroupCodeValueSubValue(groupCode, value, subValue);
    }

    @Override
    public List<TytSourceVO> getListByGroupCodeList(List<String> groupCodeList) {
        return BeanUtil.copyToList(sourceService.getListByGroupCodeList(groupCodeList), TytSourceVO.class);
    }
}