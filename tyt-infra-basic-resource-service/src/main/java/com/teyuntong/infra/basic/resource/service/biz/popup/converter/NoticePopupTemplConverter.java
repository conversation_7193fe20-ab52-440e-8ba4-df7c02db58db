package com.teyuntong.infra.basic.resource.service.biz.popup.converter;

import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupTemplDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/28 14:12
 */
@Mapper
public interface NoticePopupTemplConverter {
    NoticePopupTemplConverter INSTANCE = Mappers.getMapper(NoticePopupTemplConverter.class);

    /**
     * DO转VO
     */
    NoticePopupTemplVo DO2VO(NoticePopupTemplDO noticePopupTemplDO);

    /**
     * DOList转VOList
     */
    List<NoticePopupTemplVo> DOs2VOs(List<NoticePopupTemplDO> noticePopupTemplDOList);

}
