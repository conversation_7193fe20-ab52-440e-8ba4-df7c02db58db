package com.teyuntong.infra.basic.resource.service.rpc.popup;


import com.teyuntong.infra.basic.resource.client.popup.dto.NoticePopupDTO;
import com.teyuntong.infra.basic.resource.client.popup.service.NoticePopupRpcService;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupDO;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupTemplDO;
import com.teyuntong.infra.basic.resource.service.biz.popup.service.NoticePopupService;
import com.teyuntong.infra.basic.resource.service.biz.popup.service.NoticePopupTemplService;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 弹窗通知表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */
@RestController
@RequiredArgsConstructor
public class NoticePopupRpcServiceImpl implements NoticePopupRpcService {

    private final NoticePopupService noticePopupService;
    private final NoticePopupTemplService noticePopupTemplService;


    @Override
    public void saveNoticePopup(NoticePopupDTO noticePopupDTO) {
        NoticePopupDO noticePopup = new NoticePopupDO();
        if (StringUtils.isNotBlank(noticePopupDTO.getParams())){
            noticePopup.setContent(noticePopupDTO.getParams());
        }
        noticePopup.setProductionId(noticePopupDTO.getProductionId());
        noticePopup.setReceiveId(noticePopupDTO.getReceiveId());
        NoticePopupTemplDO tmpl = noticePopupTemplService.getByType(noticePopupDTO.getPopupType().getType1(), noticePopupDTO.getPopupType().getType2());
        if (tmpl!=null){
            noticePopup.setTemplId(tmpl.getId());
            noticePopup.setTitle(tmpl.getTitle());
            noticePopup.setType(tmpl.getType1());
        }
        noticePopup.setReceiveStatus(1);
        noticePopup.setCtime(new Date());
        noticePopup.setStatus(1);
        noticePopup.setOriginPopup(0);
        noticePopup.setCarPopup(0);
        noticePopup.setGoodsPopup(0);
        if (noticePopupDTO.getPort() == 1){
            noticePopup.setCarPopup(1);
        }else {
            noticePopup.setGoodsPopup(1);
        }
        noticePopupService.saveNoticePopup(noticePopup);

    }
}
