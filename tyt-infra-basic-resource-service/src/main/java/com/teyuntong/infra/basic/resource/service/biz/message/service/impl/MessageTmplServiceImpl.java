package com.teyuntong.infra.basic.resource.service.biz.message.service.impl;

import com.teyuntong.infra.basic.resource.service.biz.message.mybatis.entity.MessageTmplDO;
import com.teyuntong.infra.basic.resource.service.biz.message.mybatis.mapper.MessageTmplMapper;
import com.teyuntong.infra.basic.resource.service.biz.message.service.MessageTmplService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 消息模版表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class MessageTmplServiceImpl implements MessageTmplService {

    private final MessageTmplMapper messageTmplMapper;

    @Override
    public MessageTmplDO getByKey(String key) {
        return messageTmplMapper.getByKey(key);
    }
}
