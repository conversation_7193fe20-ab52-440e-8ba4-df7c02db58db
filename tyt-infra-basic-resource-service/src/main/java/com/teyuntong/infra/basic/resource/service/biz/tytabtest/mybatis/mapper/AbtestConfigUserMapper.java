package com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity.AbtestConfigUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ab测试用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-26
 */
@Mapper
public interface AbtestConfigUserMapper extends BaseMapper<AbtestConfigUserDO> {

    AbtestConfigUserDO selectByABTestIdAndUserId(@Param("abTestId") Long abTestId, @Param("userId") Long userId);

}
