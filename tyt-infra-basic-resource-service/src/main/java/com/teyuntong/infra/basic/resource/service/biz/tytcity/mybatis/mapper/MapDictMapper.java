package com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.MapDictDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 地图距离字典 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-21
 */
@Mapper
public interface MapDictMapper extends BaseMapper<MapDictDO> {

    MapDictDO getMapDict(@Param("mapDictKey") String mapDictKey);
}
