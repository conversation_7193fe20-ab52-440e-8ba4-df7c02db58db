package com.teyuntong.infra.basic.resource.service.biz.tytabtest.service;

import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.AbTestConfigVO;

import java.util.List;

public interface ABTestService {

    /**
     * Returns a list of ABTestVo objects based on the given ABTestDto object.
     *
     * @param abTestReq the ABTestDto object containing the AB test code list and user ID
     * @return a list of ABTestVo objects
     */
    List<ABTestVo> getUserTypeList(ABTestDto abTestReq);

    AbTestConfigVO getAbTestConfig(String code);

    /**
     * 更新userType
     */
    Integer updateUserType(String code, Long userId, Integer userType);
}
