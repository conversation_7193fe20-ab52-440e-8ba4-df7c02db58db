package com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 公共信息资源表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-13
 */
@Getter
@Setter
@TableName("tyt_public_resource")
public class PublicResourceDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String name;

    private String value;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String remark;
}
