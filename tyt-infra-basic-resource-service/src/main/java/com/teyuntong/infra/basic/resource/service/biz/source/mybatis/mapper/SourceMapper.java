package com.teyuntong.infra.basic.resource.service.biz.source.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import com.teyuntong.infra.basic.resource.service.biz.source.mybatis.entity.SourceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公用信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Mapper
public interface SourceMapper extends BaseMapper<SourceDO> {

    List<SourceDO> getListByGroupCode(@Param("groupCode") String groupCode);

    List<SourceDO> getListByGroupCodeList(@Param("groupCodeList")List<String> groupCodeList);

    TytSourceVO getByGroupCodeAndValue(@Param("groupCode") String groupCode, @Param("value") String value);

    String getSubGroupCode(@Param("parent") String parent);
}
