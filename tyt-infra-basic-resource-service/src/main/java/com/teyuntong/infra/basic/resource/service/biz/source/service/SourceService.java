package com.teyuntong.infra.basic.resource.service.biz.source.service;


import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import com.teyuntong.infra.basic.resource.service.biz.source.mybatis.entity.SourceDO;

import java.util.Collection;
import java.util.List;

/**
 * 公用信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
public interface SourceService {
    /**
     * 根据分组编码获取列表
     *
     * @param groupCode
     * @return
     */
    Collection<?> getListByGroupCode(String groupCode);

    /**
     * 根据分组编码列表批量获取列表
     *
     * @param groupCodeList
     * @return
     */
    List<SourceDO> getListByGroupCodeList(List<String> groupCodeList);

    TytSourceVO getByGroupCodeAndValue(String groupCode, String value);

    TytSourceVO getByGroupCodeValueSubValue(String groupCode, String value, String subValue);
}
