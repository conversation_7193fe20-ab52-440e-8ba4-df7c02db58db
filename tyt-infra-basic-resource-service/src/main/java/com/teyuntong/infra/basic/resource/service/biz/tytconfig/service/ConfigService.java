package com.teyuntong.infra.basic.resource.service.biz.tytconfig.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.entity.ConfigDO;

import java.util.List;
import java.util.Map;

/**
 * @author: helian
 * @since: 2023/12/26 14:40
 */
public interface ConfigService extends IService<ConfigDO> {
    /**
     * 根据名称获取配置信息
     *
     * @param name
     * @return
     */
    ConfigDO getByName(String name);

    /**
     * 根据name获取value的number
     *
     * @param name
     * @return
     */
    Integer getIntValue(String name);

    /**
     * 根据name获取value的number,不存在返回默认
     *
     * @param name
     * @return
     */
    Integer getIntValue(String name, int defaultValue);

    /**
     * 根据name获取value的string
     *
     * @param name
     * @return
     */
    String getStringValue(String name);

    /**
     * 根据name获取value的string,不存在返回默认
     *
     * @param name
     * @return
     */
    String getStringValue(String name, String defaultValue);

    /**
     * 更新
     *
     * @param configDO
     * @return
     */
    boolean update(ConfigDO configDO);

    /**
     * 删除缓存
     *
     * @param name
     * @return
     */
    boolean deleteByName(String name);


    /**
     * 查询所有配置信息
     *
     * @return
     */
    List<ConfigDO> selectAll();

    /**
     * 批量查询配置信息
     *
     * @return
     */
    Map<String, String> getByNameList(List<String> nameList);
}
