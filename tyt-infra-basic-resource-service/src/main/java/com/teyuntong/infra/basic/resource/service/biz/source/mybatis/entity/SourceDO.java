package com.teyuntong.infra.basic.resource.service.biz.source.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 公用信息表
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Getter
@Setter
@TableName("tyt_source")
public class SourceDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分组代码
     */
    private String groupCode;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 属性值
     */
    private String value;

    /**
     * 属性名称
     */
    private String name;

    /**
     * 属性简称
     */
    private String shortName;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 父级编码
     */
    private String parent;

    /**
     * 0是正常1是无效
     */
    private Integer status;

    /**
     * 字典状态0是正常1是无效
     */
    private Integer dictStatus;
}
