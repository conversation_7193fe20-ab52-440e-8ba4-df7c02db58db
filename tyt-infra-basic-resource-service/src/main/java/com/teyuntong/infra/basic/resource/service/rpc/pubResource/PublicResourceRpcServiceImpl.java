package com.teyuntong.infra.basic.resource.service.rpc.pubResource;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.infra.basic.resource.client.pubresource.service.PublicResourceRpcService;
import com.teyuntong.infra.basic.resource.client.pubresource.vo.PublicResourceVO;
import com.teyuntong.infra.basic.resource.service.biz.pubresource.converter.PublicResourceConverter;
import com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity.PublicResourceDO;
import com.teyuntong.infra.basic.resource.service.biz.pubresource.service.PublicResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 公共信息资源表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-13
 */

@Slf4j
@RestController
@RequiredArgsConstructor
public class PublicResourceRpcServiceImpl implements PublicResourceRpcService {

    private final PublicResourceService publicResourceService;

    @Override
    public PublicResourceVO getByName(String paramName) {
        PublicResourceDO publicResourceDO = publicResourceService.getByName(paramName);
        return PublicResourceConverter.INSTANCE.DO2VO(publicResourceDO);
    }

    @Override
    public List<PublicResourceVO> getByNameList(List<String> paramNameList) {
        List<PublicResourceVO> resourceVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(paramNameList)) {
            paramNameList.forEach(
                    paramName -> {
                        PublicResourceVO publicResourceVO = getByName(paramName);
                        if (publicResourceVO != null) {
                            resourceVOList.add(publicResourceVO);
                        }
                    }
            );
        }
        return resourceVOList;
    }

    @Override
    public List<PublicResourceVO> getAll() {
        List<PublicResourceDO> publicResourceDOList = publicResourceService.getAll();
        return PublicResourceConverter.INSTANCE.DOs2VOs(publicResourceDOList);
    }
}
