package com.teyuntong.infra.basic.resource.service.rpc.sequence;

import com.teyuntong.infra.basic.resource.client.sequence.service.TytSequenceRpcService;
import com.teyuntong.infra.basic.resource.service.biz.sequence.service.SequenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/2/18 15:41
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class TytSequenceRpcServiceImpl implements TytSequenceRpcService {

    private final SequenceService sequenceService;

    /**
     * 发号器发号
     *
     * @param name
     * @return
     */
    @Override
    public String getSequence(String name) {
        return sequenceService.getSequence(name);
    }


}
