package com.teyuntong.infra.basic.resource.service.rpc.message;


import cn.hutool.core.bean.BeanUtil;
import com.teyuntong.infra.basic.resource.client.message.service.MessageTmplRpcService;
import com.teyuntong.infra.basic.resource.client.message.vo.MessageTmplRpcVO;
import com.teyuntong.infra.basic.resource.service.biz.message.service.MessageTmplService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短信模板RPC服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-15 17:46:59
 */
@RestController
@RequiredArgsConstructor
public class MessageTmplRpcServiceImpl implements MessageTmplRpcService {

    private final MessageTmplService messageTmplService;

    @Override
    public MessageTmplRpcVO getTmplByKey(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return BeanUtil.copyProperties(messageTmplService.getByKey(key), MessageTmplRpcVO.class);
    }
}