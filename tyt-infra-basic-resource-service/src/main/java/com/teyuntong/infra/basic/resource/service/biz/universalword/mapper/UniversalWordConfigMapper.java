package com.teyuntong.infra.basic.resource.service.biz.universalword.mapper;

import com.teyuntong.infra.basic.resource.service.biz.universalword.mapper.entity.UniversalWordConfigInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UniversalWordConfigMapper {

    /**
     * 通用文案配置的type字段含义修改为可以保存多个端，例如type值为12，表示车货app都使用
     * @param type
     * @return
     */
    List<UniversalWordConfigInfoDO> getAllUniversalWordConfigInfoListByType(@Param("type") Integer type);

    List<UniversalWordConfigInfoDO> getAllUniversalWordConfigInfoListByCodeList(@Param("codes") List<String> codes);
}
