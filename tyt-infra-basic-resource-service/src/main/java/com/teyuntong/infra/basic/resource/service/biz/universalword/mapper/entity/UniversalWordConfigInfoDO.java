package com.teyuntong.infra.basic.resource.service.biz.universalword.mapper.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UniversalWordConfigInfoDO {

    private Long id;

    //位置/触发条件
    private String site;

    private String code;

    //内容
    private String content;

    private String remark;

    //所属方 1:车APP，2:货APP
    private Integer type;

    //是否删除状态 0:未删除，1:已删除
    private Integer deleteStatus;

    //启用禁用状态 0:禁用，1:启用
    private Integer status;

    private Long modifyUserId;

    private String modifyUserName;

    private Date ctime;

    private Date mtime;
}
