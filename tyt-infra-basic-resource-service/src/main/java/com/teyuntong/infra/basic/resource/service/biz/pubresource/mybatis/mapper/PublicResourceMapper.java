package com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity.PublicResourceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 公共信息资源表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-13
 */
@Mapper
public interface PublicResourceMapper extends BaseMapper<PublicResourceDO> {
    /**
     * 根据名称获取公共资源
     *
     * @param name
     * @return
     */
    PublicResourceDO getByName(@Param("name") String name);

    /**
     * 根据名称列表获取公共资源列表
     *
     * @param nameList
     * @return
     */
    List<PublicResourceDO> getByNameList(@Param("nameList") List<String> nameList);

    /**
     * 获取所有公共资源
     *
     * @return
     */
    List<PublicResourceDO> getAll();
}
