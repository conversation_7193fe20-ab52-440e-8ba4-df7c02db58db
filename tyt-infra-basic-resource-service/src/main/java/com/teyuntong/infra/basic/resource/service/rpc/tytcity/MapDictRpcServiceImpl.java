package com.teyuntong.infra.basic.resource.service.rpc.tytcity;

import com.teyuntong.infra.basic.resource.client.tytcity.dto.MapDictDto;
import com.teyuntong.infra.basic.resource.client.tytcity.service.MapDictRpcService;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.MapDictVo;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.MapDictDO;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.service.MapDictService;
import com.teyuntong.infra.basic.resource.service.common.utils.MD5Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/02/05 18:24
 */
@Slf4j
@RequiredArgsConstructor
@RestController
public class MapDictRpcServiceImpl implements MapDictRpcService {

    private final MapDictService mapDictService;

    @Override
    public MapDictVo getMapDict(MapDictDto mapDictDto) {
        MapDictVo mapDictVo = new MapDictVo();
        String mapDictKey = getMapDictKey(mapDictDto);
        if (StringUtils.isNotBlank(mapDictKey)) {
            MapDictDO mapDict = mapDictService.getMapDict(mapDictKey);
            if (mapDict != null) {
                BeanUtils.copyProperties(mapDict, mapDictVo);
                return mapDictVo;
            }
        }
        return mapDictVo;
    }


    private String getMapDictKey(MapDictDto mapDictDto) {
        if (StringUtils.isNotBlank(mapDictDto.getStartProvinc())
                && StringUtils.isNotBlank(mapDictDto.getStartCity())
                && StringUtils.isNotBlank(mapDictDto.getDestProvinc())
                && StringUtils.isNotBlank(mapDictDto.getDestCity())) {
            StringBuffer sb = new StringBuffer();
            sb.append(mapDictDto.getStartProvinc());
            sb.append("-");
            sb.append(mapDictDto.getStartCity());
            if (StringUtils.isNotBlank(mapDictDto.getStartArea())) {
                sb.append("-");
                sb.append(mapDictDto.getStartArea());
            }
            sb.append("__");
            sb.append(mapDictDto.getDestProvinc());
            sb.append("-");
            sb.append(mapDictDto.getDestCity());
            if (StringUtils.isNotBlank(mapDictDto.getDestArea())) {
                sb.append("-");
                sb.append(mapDictDto.getDestArea());
            }
            return MD5Util.GetMD5Code(sb.toString());
        }
        return null;
    }
}
