package com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity.AbtestConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * ab测试配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-26
 */
@Mapper
public interface AbtestConfigMapper extends BaseMapper<AbtestConfigDO> {
    /**
     * 根据code列表查询启用的AB测试配置
     *
     * @param codeList
     * @return
     */
    List<AbtestConfigDO> selectEnableABTestByCodeList(@Param("codeList") List<String> codeList);

    AbtestConfigDO selectByCode(@Param("code") String code);
}
