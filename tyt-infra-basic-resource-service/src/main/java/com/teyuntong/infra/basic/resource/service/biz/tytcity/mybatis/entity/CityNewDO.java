package com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-05
 */
@Getter
@Setter
@TableName("tyt_city_new")
public class CityNewDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String areaCode;

    private String parentAreaCode;

    private String level;

    /**
     * 地区名称
     */
    private String areaName;

    /**
     * 高德地图地区名称
     */
    private String mapAreaName;

    /**
     * 地区编码
     */
    private String mapCode;

    private String cityName;

    /**
     * 高德地图市区名称
     */
    private String mapCityName;

    private String provinceName;

    /**
     * 高德地图省级名称
     */
    private String mapProvinceName;

    private String rf;

    private String px;

    private String py;

    private String longitude;

    private String latitude;

    private String shortName;

    private String standardName;

    /**
     * 高德地图对应经度
     */
    private String mapLongitude;

    /**
     * 高德地图对应纬度
     */
    private String mapLatitude;

    private Date createTime;
}
