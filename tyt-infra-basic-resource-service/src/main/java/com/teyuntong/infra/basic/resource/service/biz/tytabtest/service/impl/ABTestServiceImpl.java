package com.teyuntong.infra.basic.resource.service.biz.tytabtest.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.AbTestConfigVO;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity.AbtestConfigDO;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity.AbtestConfigUserDO;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.mapper.AbtestConfigMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.mapper.AbtestConfigUserMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.service.ABTestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Service
@Slf4j
public class ABTestServiceImpl implements ABTestService {


    private final AbtestConfigMapper abtestConfigMapper;

    private final AbtestConfigUserMapper abtestConfigUserMapper;

    @Override
    public AbTestConfigVO getAbTestConfig(String code) {
        if (StringUtils.isNotBlank(code)) {
            AbtestConfigDO configDO = abtestConfigMapper.selectByCode(code);
            if (Objects.nonNull(configDO)) {
                AbTestConfigVO configVO = new AbTestConfigVO();
                BeanUtils.copyProperties(configDO, configVO);
                return configVO;
            }
        }
        return null;
    }

    @Override
    public List<ABTestVo> getUserTypeList(ABTestDto abTestReq) {
        List<ABTestVo> result = new ArrayList<>();
        if (abTestReq == null || CollUtil.isEmpty(abTestReq.getCodeList()) || abTestReq.getUserId() == null) {
            return result;
        }
        List<AbtestConfigDO> abtestConfigDOList = abtestConfigMapper.selectEnableABTestByCodeList(abTestReq.getCodeList());
        List<String> configCodeList = abtestConfigDOList.stream().map(AbtestConfigDO::getCode).toList();

        for (String code : abTestReq.getCodeList()) {
            Integer typeResult = 0;
            ABTestVo tytAbtestConfigVo = new ABTestVo();
            if (CollUtil.isEmpty(abtestConfigDOList)) {
                tytAbtestConfigVo.setCode(code);
                tytAbtestConfigVo.setType(typeResult);
                result.add(tytAbtestConfigVo);
                continue;
            }

            if (!configCodeList.contains(code)) {
                tytAbtestConfigVo.setCode(code);
                tytAbtestConfigVo.setType(typeResult);
                result.add(tytAbtestConfigVo);
                continue;
            }

            for (AbtestConfigDO abTestDO : abtestConfigDOList) {
                if (code.equals(abTestDO.getCode())) {
                    typeResult = abTestDO.getDefaultType();
                    if (abTestDO.getRuleType() != 0) {
                        AbtestConfigUserDO tytAbtestConfigUser = abtestConfigUserMapper.selectByABTestIdAndUserId(abTestDO.getId(), abTestReq.getUserId());
                        if (tytAbtestConfigUser != null) {
                            typeResult = tytAbtestConfigUser.getType();
                            tytAbtestConfigVo.setImportModifyTime(tytAbtestConfigUser.getModifyTime());
                        }
                    }
                    tytAbtestConfigVo.setCode(code);
                    tytAbtestConfigVo.setType(typeResult);
                    result.add(tytAbtestConfigVo);
                }
            }
        }
        return result;
    }


    /**
     * 更新userType，删掉缓存
     */
    @Override
    @CacheEvict(value = "abtest", key = "'getUserType:' + #code + ':' + #userId")
    public Integer updateUserType(String code, Long userId, Integer userType) {
        AbtestConfigDO abtestConfigDO = abtestConfigMapper.selectByCode(code);
        if (abtestConfigDO == null) {
            return 0;
        }
        AbtestConfigUserDO abtestUserDO = abtestConfigUserMapper.selectByABTestIdAndUserId(abtestConfigDO.getId(), userId);
        if (abtestUserDO == null) {
            return 0;
        }
        AbtestConfigUserDO updateUserDO = new AbtestConfigUserDO();
        updateUserDO.setId(abtestUserDO.getId());
        updateUserDO.setType(userType);
        updateUserDO.setModifyTime(new Date());
        return abtestConfigUserMapper.updateById(updateUserDO);
    }

}
