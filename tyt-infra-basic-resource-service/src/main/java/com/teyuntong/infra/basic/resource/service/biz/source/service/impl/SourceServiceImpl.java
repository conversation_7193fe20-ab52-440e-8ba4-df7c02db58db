package com.teyuntong.infra.basic.resource.service.biz.source.service.impl;

import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import com.teyuntong.infra.basic.resource.service.biz.source.mybatis.entity.SourceDO;
import com.teyuntong.infra.basic.resource.service.biz.source.mybatis.mapper.SourceMapper;
import com.teyuntong.infra.basic.resource.service.biz.source.service.SourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 公用信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class SourceServiceImpl implements SourceService {

    private final SourceMapper sourceMapper;

    @Override
    public Collection<?> getListByGroupCode(String groupCode) {
        return sourceMapper.getListByGroupCode(groupCode);
    }

    @Override
    public TytSourceVO getByGroupCodeAndValue(String groupCode, String value) {
        return sourceMapper.getByGroupCodeAndValue(groupCode, value);
    }

    @Override
    public TytSourceVO getByGroupCodeValueSubValue(String groupCode, String value, String subValue) {
        TytSourceVO parent = this.getByGroupCodeAndValue(groupCode, value);
        if (Objects.isNull(parent)) {
            return null;
        }
        String subGroupCode = sourceMapper.getSubGroupCode(parent.getId().toString());
        return this.getByGroupCodeAndValue(subGroupCode, subValue);
    }

    @Override
    public List<SourceDO> getListByGroupCodeList(List<String> groupCodeList) {
        return sourceMapper.getListByGroupCodeList(groupCodeList);
    }
}
