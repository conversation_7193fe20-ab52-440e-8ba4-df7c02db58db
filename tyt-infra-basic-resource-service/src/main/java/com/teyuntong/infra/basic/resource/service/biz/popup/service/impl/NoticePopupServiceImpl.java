package com.teyuntong.infra.basic.resource.service.biz.popup.service.impl;

import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupDO;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.mapper.NoticePopupMapper;
import com.teyuntong.infra.basic.resource.service.biz.popup.service.NoticePopupService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 弹窗通知表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class NoticePopupServiceImpl implements NoticePopupService {

    private final NoticePopupMapper noticePopupMapper;

    @Override
    public void saveNoticePopup(NoticePopupDO noticePopup) {
        noticePopupMapper.insert(noticePopup);
    }
}
