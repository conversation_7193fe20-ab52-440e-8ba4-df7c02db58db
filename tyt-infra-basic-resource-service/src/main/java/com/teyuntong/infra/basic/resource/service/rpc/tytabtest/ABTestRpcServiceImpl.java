package com.teyuntong.infra.basic.resource.service.rpc.tytabtest;

import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.service.ABTestRpcService;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.AbTestConfigVO;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.service.ABTestCacheService;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.service.ABTestService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
public class ABTestRpcServiceImpl implements ABTestRpcService {

    private final ABTestService abTestService;
    private final ABTestCacheService abTestCacheService;

    @Override
    public List<ABTestVo> getUserTypeList(ABTestDto abTestReq) {
        return abTestService.getUserTypeList(abTestReq);
    }

    @Override
    public AbTestConfigVO getAbTestConfig(String code) {
        return abTestService.getAbTestConfig(code);
    }

    @Override
    public Integer getUserType(String code, Long userId) {
        return abTestCacheService.getUserType(code, userId);
    }

    @Override
    public Integer updateUserType(String code, Long userId, Integer userType) {
        return abTestService.updateUserType(code, userId, userType);
    }
}
