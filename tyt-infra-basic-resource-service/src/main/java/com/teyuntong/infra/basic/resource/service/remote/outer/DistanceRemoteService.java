package com.teyuntong.infra.basic.resource.service.remote.outer;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.outer.export.service.client.distance.service.DistanceRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-outer-export-service", path = "outer-export", contextId = "DistanceRemoteService",
        fallbackFactory = DistanceRemoteService.DistanceRemoteFallbackFactory.class)
public interface DistanceRemoteService extends DistanceRpcService {

    @Component
    class DistanceRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<DistanceRemoteService>{
        protected DistanceRemoteFallbackFactory() {
            super(true, DistanceRemoteService.class);
        }
    }
}