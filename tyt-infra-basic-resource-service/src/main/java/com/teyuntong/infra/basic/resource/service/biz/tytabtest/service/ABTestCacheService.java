package com.teyuntong.infra.basic.resource.service.biz.tytabtest.service;

/**
 * AB测试缓存服务接口
 */
public interface ABTestCacheService {

    /**
     * 根据AB测试Code和用户ID获取这个用户这个AB测试的Type值，如果AB测试不存在则Type返回0
     *
     * @param code   the AB test code
     * @param userId the user ID
     * @return the user type, or 0 if code is blank or userId is null
     */
    Integer getUserType(String code, Long userId);
}
