package com.teyuntong.infra.basic.resource.service.biz.tytcity.service.impl;

import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.MapDictDO;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.mapper.MapDictMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.service.MapDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 地图距离字典 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-21
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class MapDictServiceImpl implements MapDictService {

    private final MapDictMapper mapDictMapper;

    @Override
    public MapDictDO getMapDict(String mapDictKey) {
        return mapDictMapper.getMapDict(mapDictKey);
    }
}
