package com.teyuntong.infra.basic.resource.service.biz.popup.service.impl;

import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupTemplDO;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.mapper.NoticePopupTemplMapper;
import com.teyuntong.infra.basic.resource.service.biz.popup.service.NoticePopupTemplService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 弹窗模板表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class NoticePopupTemplServiceImpl implements NoticePopupTemplService {

    private final NoticePopupTemplMapper noticePopupTemplMapper;

    @Override
    public NoticePopupTemplDO getByType(int type1, int type2) {
        return noticePopupTemplMapper.getByType(type1, type2);
    }
}
