package com.teyuntong.infra.basic.resource.service.biz.tytcity.service;


import com.teyuntong.infra.basic.resource.client.tytcity.dto.TytCityDto;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO;

public interface TytCityService {

    TytCityDO getCityDataByCondition(TytCityDto tytCityDto);

    /**
     * 根据省市区获取
     *
     * @param address 北京市海淀区 或 河北保定市竞秀区
     */
    TytCityDO getByAddress(String address);

    /**
     * 根据城市名称和区域名称获取数据
     *
     * @param cityName
     * @param areaName
     * @param level
     * @return
     */
    TytCityDO getRegxByName(String cityName, String areaName, String level);

    /**
     * 获取城市简称
     *
     * @param cityName
     * @return
     */
    TytCityDO getShortCityName(String cityName);

    /**
     * 根据坐标获取地址信息
     *
     * @param px
     * @param py
     * @param level
     * @return
     */
    TytCityDO getCityByXY(String px, String py, String level);
}
