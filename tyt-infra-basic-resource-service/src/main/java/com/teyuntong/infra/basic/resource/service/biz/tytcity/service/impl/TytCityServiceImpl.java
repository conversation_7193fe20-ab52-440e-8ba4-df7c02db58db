package com.teyuntong.infra.basic.resource.service.biz.tytcity.service.impl;

import com.teyuntong.infra.basic.resource.client.tytcity.dto.TytCityDto;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.mapper.TytCityMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.service.TytCityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class TytCityServiceImpl implements TytCityService {


    private final TytCityMapper tytCityMapper;


    @Override
    public TytCityDO getCityDataByCondition(TytCityDto tytCityDto) {
        return tytCityMapper.getCityDataByCondition(tytCityDto);
    }

    /**
     * 根据省市区获取
     *
     * @param address 北京市海淀区 或 河北保定市竞秀区
     */
    @Override
    public TytCityDO getByAddress(String address) {
        if (StringUtils.isBlank(address)) {
            return null;
        }
        boolean isMunicipality = address.startsWith("北京市") || address.startsWith("天津市")
                || address.startsWith("上海市") || address.startsWith("重庆市");
        return tytCityMapper.getByAddress(isMunicipality, address);
    }

    @Override
    public TytCityDO getRegxByName(String cityName, String areaName,String level) {
        return tytCityMapper.getRegxByName(cityName, areaName,level);
    }

    @Override
    public TytCityDO getShortCityName(String cityName) {
        return tytCityMapper.getShortCityName(cityName);
    }

    @Override
    public TytCityDO getCityByXY(String px, String py,String level) {
        return tytCityMapper.getCityByXY(px,py,level);
    }
}
