package com.teyuntong.infra.basic.resource.service.biz.tytconfig.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.teyuntong.infra.basic.resource.service.common.constant.RedisConstant;
import com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.entity.ConfigDO;
import com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.mapper.ConfigMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytconfig.service.ConfigService;
import com.teyuntong.infra.basic.resource.service.common.utils.ApplicationContextUtil;
import com.teyuntong.infra.common.cache.redis.ExpireCache;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: helian
 * @since: 2023/12/26 14:47
 */
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = RedisConstant.CONFIG_CACHE_NAME)
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, ConfigDO> implements ConfigService {

    private final ConfigMapper configMapper;


    @Override
    public ConfigDO getByName(String name) {
        return configMapper.selectByName(name);
    }

    @Override
    public Integer getIntValue(String name) {
        String stringValue = ApplicationContextUtil.getBean(ConfigService.class).getStringValue(name);
        return StringUtils.isNotBlank(stringValue) ? Integer.parseInt(stringValue) : null;
    }

    @Override
    public Integer getIntValue(String name, int defaultValue) {
        Integer value = getIntValue(name);
        return value != null ? value : defaultValue;
    }

    @Override
    @Cacheable(key = "#name")
    @ExpireCache(ttl = 10)
    public String getStringValue(String name) {
        ConfigDO configDO = getByName(name);
        return configDO != null ? configDO.getValue() : null;
    }

    @Override
    public String getStringValue(String name, String defaultValue) {
        String value = ApplicationContextUtil.getBean(ConfigService.class).getStringValue(name);
        return StringUtils.isNotBlank(value) ? value : defaultValue;
    }


    @Override
    @CacheEvict(key = "#configDO.name")
    public boolean update(ConfigDO configDO) {
        return this.updateById(configDO);
    }

    @Override
    @CacheEvict(key = "#name")
    public boolean deleteByName(String name) {
        return configMapper.deleteByName(name) > 0;
    }

    @Override
    public List<ConfigDO> selectAll() {
        return configMapper.selectAll();
    }

    @Override
    public Map<String, String> getByNameList(List<String> nameList) {
        Map<String, String> map = new HashMap<>();
        if (CollUtil.isEmpty(nameList)) {
            ConfigService configService = ApplicationContextUtil.getBean(ConfigService.class);
            nameList.forEach(name -> {
                String value = configService.getStringValue(name);
                map.put(name, value);
            });
        }
        return map;
    }
}
