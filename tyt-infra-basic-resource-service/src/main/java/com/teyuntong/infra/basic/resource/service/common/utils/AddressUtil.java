package com.teyuntong.infra.basic.resource.service.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2025/02/05 17:28
 */
public class AddressUtil {

    public static List<String> parseAddress(String address) {
        List<String> result = new ArrayList<>();
        result.add(address.substring(0,2));
        Pattern pattern = Pattern.compile("([^省]*+省)?([^市]*+市)?([^区]*+区)?([^县]*+县)?");
        Matcher matcher = pattern.matcher(address);
        if (matcher.find()) {
            for (int i = 1; i <= matcher.groupCount(); i++) {
                result.add(matcher.group(i));
            }
        }
        return result;
    }
}
