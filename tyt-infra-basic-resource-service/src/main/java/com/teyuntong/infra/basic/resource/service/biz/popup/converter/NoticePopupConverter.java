package com.teyuntong.infra.basic.resource.service.biz.popup.converter;

import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupVo;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/28 14:12
 */
@Mapper
public interface NoticePopupConverter {
    NoticePopupConverter INSTANCE = Mappers.getMapper(NoticePopupConverter.class);

    /**
     * DO转VO
     */
    NoticePopupVo DO2VO(NoticePopupDO noticePopupDO);

    /**
     * DOList转VOList
     */
    List<NoticePopupVo> DOs2VOs(List<NoticePopupDO> noticePopupDOList);

}
