package com.teyuntong.infra.basic.resource.service.biz.pubresource.converter;

import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.basic.resource.client.pubresource.vo.PublicResourceVO;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupTemplDO;
import com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity.PublicResourceDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/28 14:12
 */
@Mapper
public interface PublicResourceConverter {
    PublicResourceConverter INSTANCE = Mappers.getMapper(PublicResourceConverter.class);

    /**
     * DO转VO
     */
    PublicResourceVO DO2VO(PublicResourceDO publicResourceDO);

    /**
     * DOList转VOList
     */
    List<PublicResourceVO> DOs2VOs(List<PublicResourceDO> publicResourceDOList);

}
