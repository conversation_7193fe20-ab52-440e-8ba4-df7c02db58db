package com.teyuntong.infra.basic.resource.service.common.utils;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-04-08
 */
@Component
public class ApplicationContextUtil implements ApplicationContextAware {
    private static volatile ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        ApplicationContextUtil.applicationContext = applicationContext;
        synchronized (ApplicationContextUtil.class) {
            ApplicationContextUtil.class.notifyAll();
        }
    }

    public static Object getBean(String beanName) {
        checkForInitialized();
        return applicationContext.getBean(beanName);
    }

    public static <T> T getBean(Class<T> clazz) {
        checkForInitialized();
        return applicationContext.getBean(clazz);
    }

    private static void checkForInitialized() {
        if (applicationContext == null) {
            synchronized (ApplicationContextUtil.class) {
                if (applicationContext == null) {
                    try {
                        ApplicationContextUtil.class.wait();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
    }

}
