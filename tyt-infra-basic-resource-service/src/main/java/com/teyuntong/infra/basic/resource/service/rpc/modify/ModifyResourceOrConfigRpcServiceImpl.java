package com.teyuntong.infra.basic.resource.service.rpc.modify;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import com.teyuntong.infra.basic.resource.client.modify.service.ModifyResourceOrConfigRpcService;
import com.teyuntong.infra.basic.resource.client.modify.vo.ModifyResourceOrConfigVO;
import com.teyuntong.infra.basic.resource.service.common.utils.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class ModifyResourceOrConfigRpcServiceImpl implements ModifyResourceOrConfigRpcService {

    @Autowired
    private AppConfig appConfig;

    private final DingTalkClient creeperDingTalkClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?access_token=fb9219480eaf17984d28ad0d8d73b2b5e81a898d4058c88775f6e4b0549181cf");

    @Override
    public void dingMessage(ModifyResourceOrConfigVO modifyResourceOrConfigVO) {
        if (modifyResourceOrConfigVO == null || modifyResourceOrConfigVO.getType() == null
                || (modifyResourceOrConfigVO.getType() != 1 && modifyResourceOrConfigVO.getType() != 2 && modifyResourceOrConfigVO.getType() != 3)
                || StringUtils.isBlank(modifyResourceOrConfigVO.getMessage())) {
            return;
        }

        if (!"prod".equals(appConfig.getActiveProfile())) {
            return;
        }

        String type = "开关";
        if (modifyResourceOrConfigVO.getType() == 2) {
            type = "公共资源";
        } else if (modifyResourceOrConfigVO.getType() == 3) {
            type = "AB测试";
        } else if (modifyResourceOrConfigVO.getType() == 4) {
            type = "修改权益发放策略";
        }
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent("环境：" + appConfig.getActiveProfile()+ " 【" + type  + "】修改 " + modifyResourceOrConfigVO.getMessage());
        req.setText(text);
        try {
            OapiRobotSendResponse rsp = creeperDingTalkClient.execute(req, "");
        } catch (ApiException e) {
            log.info("修改AB测试开关公共资源发送钉钉消息失败 原因：", e);
        }

    }

}
