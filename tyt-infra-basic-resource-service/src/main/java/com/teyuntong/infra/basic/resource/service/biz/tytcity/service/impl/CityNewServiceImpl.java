package com.teyuntong.infra.basic.resource.service.biz.tytcity.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.CityNewDO;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.mapper.CityNewMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.service.CityNewService;
import com.teyuntong.infra.basic.resource.service.common.utils.AddressUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-05
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class CityNewServiceImpl implements CityNewService {

    private final CityNewMapper cityNewMapper;

    @Override
    public String queryMapCodeByAddress(String address) {
        List<String> list = AddressUtil.parseAddress(address);
        for (int i = list.size() - 1; i >= 0; i--) {
            LambdaQueryWrapper<CityNewDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CityNewDO::getAreaName, list.get(i));

            List<CityNewDO> cityNewDOS = cityNewMapper.selectList(wrapper);
            if (CollUtil.isNotEmpty(cityNewDOS)) {
                String mapCode = cityNewDOS.get(0).getMapCode();
                if(StringUtils.isNotBlank(mapCode)){
                    return mapCode;
                }
            }
        }
        return "110000";

    }

    public static void main(String[] args) {


        List<String> list = AddressUtil.parseAddress("南京市江北新区葛塘街道葛中路1号");
    }
}
