package com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.entity.ConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 系统相关配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2023-12-26
 */
@Mapper
public interface ConfigMapper extends BaseMapper<ConfigDO> {
    /**
     * 根据name查询配置项
     *
     * @param name
     * @return
     */
    ConfigDO selectByName(@Param("name") String name);

    /**
     * 删除配置
     *
     * @param name
     * @return
     */
    int deleteByName(@Param("name") String name);

    /**
     * 查询所有配置
     *
     * @return
     */
    List<ConfigDO> selectAll();
}
