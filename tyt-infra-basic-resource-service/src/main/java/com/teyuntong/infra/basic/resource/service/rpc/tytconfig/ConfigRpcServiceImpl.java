package com.teyuntong.infra.basic.resource.service.rpc.tytconfig;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.infra.basic.resource.client.tytconfig.dto.ConfigRpcDto;
import com.teyuntong.infra.basic.resource.client.tytconfig.service.ConfigRpcService;
import com.teyuntong.infra.basic.resource.client.tytconfig.vo.ConfigRpcVo;
import com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.entity.ConfigDO;
import com.teyuntong.infra.basic.resource.service.biz.tytconfig.service.ConfigService;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @author: helian
 * @since: 2023/12/26 17:31
 */
@RequiredArgsConstructor
@RestController
public class ConfigRpcServiceImpl implements ConfigRpcService {


    private final ConfigService configService;

    @Override
    public ConfigRpcVo getByName(String name) {
        ConfigDO configDO = configService.getByName(name);
        ConfigRpcVo configRpcVo = ConfigRpcVo.builder().build();
        BeanUtils.copyProperties(configDO, configRpcVo);
        return configRpcVo;
    }

    @Override
    public Integer getIntValue(String name) {
        return configService.getIntValue(name);
    }

    @Override
    public Integer getIntValue(String name, int defaultValue) {
        return configService.getIntValue(name, defaultValue);
    }

    @Override
    public String getStringValue(String name) {
        return configService.getStringValue(name);
    }

    @Override
    public String getStringValue(String name, String defaultValue) {
        return configService.getStringValue(name, defaultValue);
    }

    @Override
    public boolean save(ConfigRpcDto configRpcDto) {
        ConfigDO configDO = new ConfigDO();
        BeanUtils.copyProperties(configRpcDto, configDO);
        configDO.setCreateTime(DateUtil.date());
        configDO.setUpdateTime(DateUtil.date());
        return configService.save(configDO);
    }

    @Override
    public boolean update(ConfigRpcDto configRpcDto) {
        ConfigDO configDO = configService.getByName(configRpcDto.getName());
        if (configDO == null) {
            throw new BusinessException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        BeanUtils.copyProperties(configRpcDto, configDO);
        configDO.setUpdateTime(DateUtil.date());
        return configService.update(configDO);
    }

    @Override
    public boolean deleteByName(String name) {
        ConfigDO configDO = configService.getByName(name);
        if (StringUtils.isBlank(name) || configDO == null) {
            BusinessException.create(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        return configService.deleteByName(name);
    }

    @Override
    public Map<String, String> getByNameList(List<String> nameList) {
        return configService.getByNameList(nameList);
    }
}
