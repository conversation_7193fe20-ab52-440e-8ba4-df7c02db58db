package com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * ab测试配置表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-26
 */
@Getter
@Setter
@TableName("tyt_abtest_config")
public class AbtestConfigDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 启用/禁用（0禁用；1启用）
     */
    private Integer enable;

    /**
     * (0走默认;1走配置)
     */
    private Integer ruleType;

    /**
     * 默认类型
     */
    private Integer defaultType;

    /**
     * 修改人后台用户ID
     */
    private Long modifyEmployeeId;

    /**
     * 是否上传过文件进行导入用户操作，0:没有，1:有
     */
    private Integer isDoImportUser;

    /**
     * 创建名称
     */
    private String createName;

    /**
     * 修改名称
     */
    private String modifyName;
}
