package com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@TableName("tyt_city")
public class TytCityDO {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String areaCode;

    private String parentAreaCode;

    private String level;

    private String mapCode;

    private String areaName;

    private String mapAreaName;

    private String cityName;

    private String mapCityName;

    private String provinceName;

    private String mapProvinceName;

    private String rf;

    private String px;

    private String py;

    private String longitude;

    private String latitude;

    private String shortName;

    private String standardName;

    private String mapLongitude;

    private String mapLatitude;

    private String type;

    private LocalDateTime createTime;

}