package com.teyuntong.infra.basic.resource.service.biz.pubresource.service;


import com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity.PublicResourceDO;

import java.util.List;

/**
 * <p>
 * 公共信息资源表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-13
 */
public interface PublicResourceService {
    /**
     * 根据名称获取公共资源
     *
     * @param paramName
     * @return
     */
    PublicResourceDO getByName(String paramName);

    /**
     * 查询所有公共资源
     *
     * @return
     */
    List<PublicResourceDO> getAll();
}
