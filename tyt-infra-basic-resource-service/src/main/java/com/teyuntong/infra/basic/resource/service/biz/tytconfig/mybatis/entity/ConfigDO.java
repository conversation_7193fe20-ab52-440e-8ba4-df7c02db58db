package com.teyuntong.infra.basic.resource.service.biz.tytconfig.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 系统相关配置表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2023-12-26
 */
@Getter
@Setter
@TableName("tyt_config")
public class ConfigDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 键
     */
    private String name;

    private String value;

    /**
     * 单位
     */
    private String unit;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 配置类型 0服务器 1PC 2APP
     */
    private String parentType;

    /**
     * 业务类型-1：废弃配置 0：其他配置 1：货源置顶配置 2：日志开关配置  3：升级配置 4：特运通信息配置 5：财务信息配置 6：实名认证配置 7：客户端升级配置 8：信息费配置 9：货物标准化配置 10：后台导出配置 11：PC找货配置 12：PC发货配置 13：登录配置 14：邀请好友推广配置 15：轮播图配置 16：APP找货配置 17：发货配置 18：推送配置 19：身份认证配置 20：精准货源推荐配置 21：BI报表配置
     */
    private String subType;
}
