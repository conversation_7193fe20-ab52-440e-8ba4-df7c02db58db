package com.teyuntong.infra.basic.resource.service.rpc.tytcity;

import com.teyuntong.infra.basic.resource.client.tytcity.dto.TytCityDto;
import com.teyuntong.infra.basic.resource.client.tytcity.service.TytCityRpcService;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.TytCityVo;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.service.TytCityService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
public class TytCityRpcServiceImpl implements TytCityRpcService {

    private final TytCityService tytCityService;


    @Override
    public TytCityVo getCityDataByCondition(TytCityDto tytCityDto) {
        TytCityDO tytCityDO = tytCityService.getCityDataByCondition(tytCityDto);
        return covertCity(tytCityDO);
    }

    @Override
    public TytCityVo getByAddress(String address) {
        TytCityDO tytCityDO = tytCityService.getByAddress(address);
        return covertCity(tytCityDO);
    }

    @Override
    public TytCityVo getRegxByName(String cityName, String areaName, String level) {
        TytCityDO tytCityDO = tytCityService.getRegxByName(cityName, areaName, level);
        return covertCity(tytCityDO);
    }

    @Override
    public TytCityVo getShortCityName(String cityName) {
        TytCityDO tytCityDO = tytCityService.getShortCityName(cityName);
        return covertCity(tytCityDO);
    }

    @Override
    public TytCityVo getCityByXY(String px, String py, String level) {
        TytCityDO tytCityDO = tytCityService.getCityByXY(px, py, level);
        return covertCity(tytCityDO);
    }

    private TytCityVo covertCity(TytCityDO tytCityDO) {
        if (tytCityDO != null) {
            TytCityVo tytCityVo = new TytCityVo();
            BeanUtils.copyProperties(tytCityDO, tytCityVo);
            return tytCityVo;
        }
        return null;
    }


}
