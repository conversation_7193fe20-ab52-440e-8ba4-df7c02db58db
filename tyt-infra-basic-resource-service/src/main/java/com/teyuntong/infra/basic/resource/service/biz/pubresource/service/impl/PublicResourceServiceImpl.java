package com.teyuntong.infra.basic.resource.service.biz.pubresource.service.impl;

import com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.entity.PublicResourceDO;
import com.teyuntong.infra.basic.resource.service.biz.pubresource.mybatis.mapper.PublicResourceMapper;
import com.teyuntong.infra.basic.resource.service.biz.pubresource.service.PublicResourceService;
import com.teyuntong.infra.basic.resource.service.common.constant.RedisConstant;
import com.teyuntong.infra.common.cache.redis.ExpireCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 公共信息资源表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-13
 */

@Service
@Slf4j
@RequiredArgsConstructor
@CacheConfig(cacheNames = RedisConstant.PUBLIC_RESOURCE_CACHE_NAME)
public class PublicResourceServiceImpl implements PublicResourceService {

    private final PublicResourceMapper publicResourceMapper;

    @Override
    @Cacheable(key = "#name")
    @ExpireCache(ttl = 10)
    public PublicResourceDO getByName(String name) {
        return publicResourceMapper.getByName(name);
    }

    @Override
    @Cacheable
    @ExpireCache(ttl = 10)
    public List<PublicResourceDO> getAll() {
        return publicResourceMapper.getAll();
    }
}
