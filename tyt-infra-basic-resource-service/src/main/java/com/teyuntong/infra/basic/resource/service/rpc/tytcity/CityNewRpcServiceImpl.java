package com.teyuntong.infra.basic.resource.service.rpc.tytcity;

import com.teyuntong.infra.basic.resource.client.tytcity.service.CityNewRpcService;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.service.CityNewService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/02/05 18:24
 */
@Slf4j
@RequiredArgsConstructor
@RestController
public class CityNewRpcServiceImpl implements CityNewRpcService {

    private final CityNewService cityNewService;

    @Override
    public String queryMapCodeByAddress(String address) {
        log.info("queryMapCodeByAddress address:{}", address);
        String mapCode = cityNewService.queryMapCodeByAddress(address);
        log.info("queryMapCodeByAddress mapCode:{}", mapCode);
        return mapCode;
    }
}
