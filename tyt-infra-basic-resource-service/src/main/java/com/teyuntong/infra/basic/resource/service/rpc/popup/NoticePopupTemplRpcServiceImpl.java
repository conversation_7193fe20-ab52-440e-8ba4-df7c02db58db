package com.teyuntong.infra.basic.resource.service.rpc.popup;


import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.service.NoticePopupTemplRpcService;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.basic.resource.service.biz.popup.converter.NoticePopupTemplConverter;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupTemplDO;
import com.teyuntong.infra.basic.resource.service.biz.popup.service.NoticePopupTemplService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * <p>
 * 弹窗模板表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */
@RestController
@RequiredArgsConstructor
public class NoticePopupTemplRpcServiceImpl implements NoticePopupTemplRpcService {

    private final NoticePopupTemplService noticePopupTemplService;

    @Override
    public NoticePopupTemplVo getByType(PopupTypeEnum popupTypeEnum) {
        if (popupTypeEnum == null) {
            return null;
        }
        Optional<NoticePopupTemplDO> optionalDO = Optional.ofNullable(noticePopupTemplService.getByType(popupTypeEnum.getType1(), popupTypeEnum.getType2()));

        return optionalDO.map(NoticePopupTemplConverter.INSTANCE::DO2VO).orElse(null);
    }
}