package com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.mapper;

import com.teyuntong.infra.basic.resource.client.tytcity.dto.TytCityDto;
import com.teyuntong.infra.basic.resource.service.biz.tytcity.mybatis.entity.TytCityDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytCityMapper {

    TytCityDO getCityDataByCondition(@Param("tytCityDto") TytCityDto tytCityDto);

    TytCityDO getByAddress(@Param("isMunicipality") boolean isMunicipality, @Param("address") String address);

    TytCityDO getRegxByName(@Param("cityName") String cityName, @Param("areaName") String areaName, @Param("level") String level);

    TytCityDO getShortCityName(@Param("cityName") String cityName);

    TytCityDO getCityByXY(@Param("px") String px, @Param("py") String py, @Param("level") String level);
}