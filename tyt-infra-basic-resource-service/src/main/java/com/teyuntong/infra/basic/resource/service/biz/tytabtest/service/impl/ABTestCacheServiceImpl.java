package com.teyuntong.infra.basic.resource.service.biz.tytabtest.service.impl;

import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.service.ABTestCacheService;
import com.teyuntong.infra.basic.resource.service.biz.tytabtest.service.ABTestService;
import com.teyuntong.infra.basic.resource.service.common.constant.RedisConstant;
import com.teyuntong.infra.basic.resource.service.common.enums.YesNoEnum;
import com.teyuntong.infra.common.cache.redis.ExpireCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AB测试缓存服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
@CacheConfig(cacheNames = RedisConstant.ABTEST_CACHE_NAME)
public class ABTestCacheServiceImpl implements ABTestCacheService {

    private final ABTestService abTestService;

    @Override
    @Cacheable(value = "abtest", key = "'getUserType:' + #code + ':' + #userId")
    @ExpireCache(ttl = 10)
    public Integer getUserType(String code, Long userId) {
        if (StringUtils.isNotBlank(code) && userId != null) {
            ABTestDto abTestReq = new ABTestDto(List.of(code), userId);
            List<ABTestVo> userTypeList = abTestService.getUserTypeList(abTestReq);
            if (CollectionUtils.isNotEmpty(userTypeList)) {
                return userTypeList.get(0).getType();
            }
        }
        return YesNoEnum.N.getCode();
    }
}
