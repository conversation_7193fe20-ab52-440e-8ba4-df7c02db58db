package com.teyuntong.infra.basic.resource.service.biz.tytabtest.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * ab测试用户表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-26
 */
@Getter
@Setter
@TableName("tyt_abtest_config_user")
public class AbtestConfigUserDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * abtest_id
     */
    private Long abtestId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改人后台用户ID
     */
    private Long modifyEmployeeId;

    /**
     * 创建名称
     */
    private String createName;

    /**
     * 修改名称
     */
    private String modifyName;
}
