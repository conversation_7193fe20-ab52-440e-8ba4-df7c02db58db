package com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.basic.resource.service.biz.popup.mybatis.entity.NoticePopupTemplDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 弹窗模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-15
 */
@Mapper
public interface NoticePopupTemplMapper extends BaseMapper<NoticePopupTemplDO> {
    /**
     * 根据类型查询
     *
     * @param type1
     * @param type2
     * @return
     */
    NoticePopupTemplDO getByType(@Param("type1") int type1, @Param("type2") int type2);
}
