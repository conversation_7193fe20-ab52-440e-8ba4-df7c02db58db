package com.teyuntong.infra.basic.resource.service.biz.sequence.service.impl;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.infra.basic.resource.service.biz.sequence.entity.SequenceDO;
import com.teyuntong.infra.basic.resource.service.biz.sequence.mapper.SequenceMapper;
import com.teyuntong.infra.basic.resource.service.biz.sequence.service.SequenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 序列表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-18
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class SequenceServiceImpl implements SequenceService {

    private final SequenceMapper sequenceMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getSequence(String name) {
        SequenceDO sequence = sequenceMapper.getSequence(name);
        String now = DateUtil.format(new Date(), "yyMMdd");
        if (Objects.equals(sequence.getDates(), now)) {
            sequence.setNumber(sequence.getNumber() + 1);
        } else {
            sequence.setDates(now);
            sequence.setNumber(1L);
        }
        sequenceMapper.updateById(sequence);
        BigDecimal sequenceNo = new BigDecimal(sequence.getDates()).movePointRight(8).add(new BigDecimal(sequence.getNumber()));
        return sequenceNo.toPlainString();

    }
}
