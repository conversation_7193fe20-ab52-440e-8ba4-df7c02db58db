package com.teyuntong.infra.basic.resource.service.rpc.universalword;

import com.teyuntong.infra.basic.resource.client.universalword.vo.UniversalWordConfigInfoVO;
import com.teyuntong.infra.basic.resource.client.universalword.service.UniversalWordConfigRpcService;
import com.teyuntong.infra.basic.resource.service.biz.universalword.mapper.UniversalWordConfigMapper;
import com.teyuntong.infra.basic.resource.service.biz.universalword.mapper.entity.UniversalWordConfigInfoDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class UniversalWordConfigRpcServiceImpl implements UniversalWordConfigRpcService {

    @Autowired
    private UniversalWordConfigMapper universalWordConfigMapper;

    @Override
    public List<UniversalWordConfigInfoVO> getAllUniversalWordConfigInfoListByType(Integer type) {
        List<UniversalWordConfigInfoDO> universalWordConfigInfoList = universalWordConfigMapper.getAllUniversalWordConfigInfoListByType(type);
        if (CollectionUtils.isEmpty(universalWordConfigInfoList)) {
            return new ArrayList<>();
        }
        List<UniversalWordConfigInfoVO> result =  new ArrayList<>();
        for (UniversalWordConfigInfoDO universalWordConfigInfoDO : universalWordConfigInfoList) {
            UniversalWordConfigInfoVO universalWordConfigInfoVO = new UniversalWordConfigInfoVO();
            BeanUtils.copyProperties(universalWordConfigInfoDO, universalWordConfigInfoVO);
            result.add(universalWordConfigInfoVO);
        }
        return result;
    }

    @Override
    public List<UniversalWordConfigInfoVO> getAllUniversalWordConfigInfoListByCodeList(List<String> codes) {
        List<UniversalWordConfigInfoDO> universalWordConfigInfoList = universalWordConfigMapper.getAllUniversalWordConfigInfoListByCodeList(codes);
        if (CollectionUtils.isEmpty(universalWordConfigInfoList)) {
            return new ArrayList<>();
        }
        List<UniversalWordConfigInfoVO> result =  new ArrayList<>();
        for (UniversalWordConfigInfoDO universalWordConfigInfoDO : universalWordConfigInfoList) {
            UniversalWordConfigInfoVO universalWordConfigInfoVO = new UniversalWordConfigInfoVO();
            BeanUtils.copyProperties(universalWordConfigInfoDO, universalWordConfigInfoVO);
            result.add(universalWordConfigInfoVO);
        }
        return result;
    }
}
