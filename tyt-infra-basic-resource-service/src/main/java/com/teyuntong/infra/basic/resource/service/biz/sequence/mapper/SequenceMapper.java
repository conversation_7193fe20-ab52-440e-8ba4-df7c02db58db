package com.teyuntong.infra.basic.resource.service.biz.sequence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.basic.resource.service.biz.sequence.entity.SequenceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 序列表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-18
 */
@Mapper
public interface SequenceMapper extends BaseMapper<SequenceDO> {

    SequenceDO getSequence(@Param("name") String name);
}
