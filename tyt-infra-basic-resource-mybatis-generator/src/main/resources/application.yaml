spring:
  profiles:
    active: mbp-generate-local

# 创建一个名为 application-mbp-generate-local.yaml 的文件, 复制下面的内容进去, 根据自己的需求修改
# 不要将自己的 application-mbp-generate-local.yaml 上传到 gitlab, 也不要乱起其他名

#mbp:
#  generator:
#    dataSources:
#      tyt_dev:
#        url: ***********************************************************************************************************
#        username: tyt_dev
#        password: tyt_dev#20200724
#      tyt_test:
#        url: ***********************************************************************************************************
#        username: tyt_test
#        password: tyt_testDB_20200608
#    # 原配置太多了, 只拿了几个出来, 有需要的自行修改代码
#    strategy-config:
#      include:
#        - tyt_transport_main
#        - tyt_transport
#        # 是否覆盖已有 service 文件（默认 false）
#      service-override: false
#      # 是否覆盖已有 mapper 文件（默认 false）
#      mapper-override: false
#      # 是否覆盖已有 entity 文件（默认 true）
#      entity-override: true
#    global-config:
#      # 生成文件的输出目录, 默认 .generate
#      outputDir: /generate
#      # 生成文件的路径模式, relative 相对路径, 其他绝对路径
#      output-dir-type: relative
#      # 是否打开输出目录
#      open: false
#      # 作者
#      author: mybatis-plus 自动生成
#      # 开启 Kotlin 模式（默认 false）
#      kotlin: false
#      # 开启 swagger 模式（默认 false 与 springdoc 不可同时使用）
#      swagger: false
#      # 开启 springdoc 模式（默认 false 与 swagger 不可同时使用）
#      springdoc: false
#      # 时间类型对应策略
#      dateType: only_date
#      # 是否生成service 接口（默认 true）
#      serviceInterface: true
#    package-config:
#      # 父包名。如果为空，将下面子包名必须写全部， 否则就只需写子包名
#      parent: com.teyuntong.scaffold
#    template-config:
#      # 要禁用的模板类型
#      disable-types: controller
#      # 设置Service模板路径
#      service: /templates/service.java
#      # 设置ServiceImpl模板路径
#      service-impl: /templates/serviceImpl.java