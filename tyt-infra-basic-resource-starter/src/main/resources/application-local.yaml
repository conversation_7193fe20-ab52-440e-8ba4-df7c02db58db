spring:
  datasource:
    url: '***********************************************************************************************************************************************'
    username: tyt_dev
    password: tyt_dev#20200724
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 40
      minimum-idle: 15
      max-lifetime: 30000
  redis:
    database: 0
    host: public-network-dev-0.redis.rds.aliyuncs.com
    port: 16379
    password: TyT@dev#20220323
    lettuce:
      pool:
        # 最大连接数
        max-active: 8
        # 最大空闲连接数
        max-idle: 8
        # 最小空闲连接数
        min-idle: 2
  cache:
    redis:
      # 默认缓存失效时间, 30*60*1000 = 30分钟
      timeToLive: 1800000
      keyPrefix: "tytInfraBasicResourceCache:"
custom:
  feign:
    decoder:
      log-business-exception: true
      throw-business-exception: true
mybatis-plus:
  configuration:
    auto-mapping-behavior: full
  mapper-locations: classpath:mapper/*.xml
retrofit:
  circuitbreaker:
    resilience4j:
      enable: true
resilience4j:
  circuitbreaker:
    configs:
      default:
        minimumNumberOfCalls: 10
        slidingWindowSize: 60
        failureRateThreshold: 50
  timelimiter:
    configs:
      default:
        timeoutDuration: 3s
xxl-job:
  admin-addresses: http://localhost:9998/xxl-job-admin
  access-token: default_token
  appName: tyt-infra-basic-resource
  port: 9995
rocket-mq:
  consumer:
    enable: true
  producer:
    enable: true
  nameSrvAddr: http://MQ_INST_1955986389231769_BX2m3KBq.cn-beijing.mq-internal.aliyuncs.com:8080
  accessKey: LTAI5t9324vzFd6VLMybLzoE
  secretKey: ******************************

# 短信消息topic
mq-topic:
  message-center-topic: MESSAGE_CENTER_TOPIC_DEV
  message-center-tag: tag

mq-topic-other:
  short-message:
    topic: MESSAGE_CENTER_TOPIC_DEV
    tag: tag
