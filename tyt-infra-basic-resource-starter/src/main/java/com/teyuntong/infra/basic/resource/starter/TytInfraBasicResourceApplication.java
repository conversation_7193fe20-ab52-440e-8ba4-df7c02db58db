package com.teyuntong.infra.basic.resource.starter;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableFeignClients(basePackages = "com.teyuntong")
@MapperScan(basePackages = "com.teyuntong", annotationClass = Mapper.class)
@SpringBootApplication(scanBasePackages = "com.teyuntong")
public class TytInfraBasicResourceApplication {

    public static void main(String[] args) {
        SpringApplication.run(TytInfraBasicResourceApplication.class, args);
    }

}