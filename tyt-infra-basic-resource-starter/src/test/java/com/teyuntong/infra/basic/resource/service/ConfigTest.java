package com.teyuntong.infra.basic.resource.service;

import cn.hutool.json.JSONUtil;
import com.teyuntong.infra.basic.resource.TestBase;
import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.basic.resource.client.tytconfig.service.ConfigRpcService;
import com.teyuntong.infra.basic.resource.service.biz.tytconfig.service.ConfigService;
import com.teyuntong.infra.basic.resource.service.rpc.popup.NoticePopupTemplRpcServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/11/15 15:25
 */
@Slf4j
@RequiredArgsConstructor
@Disabled
public class ConfigTest extends TestBase {

    @Autowired
    private ConfigRpcService configRpcService;

    @Test
    void getByNameTest() throws Exception {
        List<String> nameList = Arrays.asList("insert_driver_bank_info_switch", "goods_diagnosis_prompt_3_x");
        Map<String, String> byNameList = configRpcService.getByNameList(nameList);
        System.out.print(JSONUtil.toJsonStr(byNameList));

    }
}
