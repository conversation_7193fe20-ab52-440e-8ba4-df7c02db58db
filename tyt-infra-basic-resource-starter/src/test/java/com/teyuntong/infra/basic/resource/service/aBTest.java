package com.teyuntong.infra.basic.resource.service;

import cn.hutool.json.JSONUtil;
import com.teyuntong.infra.basic.resource.TestBase;
import com.teyuntong.infra.basic.resource.client.pubresource.service.PublicResourceRpcService;
import com.teyuntong.infra.basic.resource.client.pubresource.vo.PublicResourceVO;
import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.service.ABTestRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @since 2024/11/15 15:25
 */
@Slf4j
@RequiredArgsConstructor
@Disabled
public class aBTest extends TestBase {

    private final ABTestRpcService abTestRpcService;

    @Test
    void getUserTypeList() throws Exception {
        ABTestDto abTestDto = new ABTestDto();
        abTestDto.setUserId(1002000780L);
        abTestDto.setCodeList(Arrays.asList("credit_wait_unlock", "user_credit_refresh"));

        mockMvc.perform(post("/ab/test/getUserTypeList")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(abTestDto)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);

    }
    @Test
    void getUserType() throws Exception {
        Integer smallMealAbTest = abTestRpcService.getUserType("SMALL_MEAL_AB_TEST", 1002000780L);
        System.out.print(smallMealAbTest);

    }
}
