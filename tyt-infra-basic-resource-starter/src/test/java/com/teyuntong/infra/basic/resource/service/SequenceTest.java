package com.teyuntong.infra.basic.resource.service;

import com.teyuntong.infra.basic.resource.TestBase;
import com.teyuntong.infra.basic.resource.client.sequence.service.TytSequenceRpcService;
import com.teyuntong.infra.basic.resource.service.biz.sequence.service.SequenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @since 2024/11/15 15:25
 */
@Slf4j
@RequiredArgsConstructor
@Disabled
public class SequenceTest extends TestBase {

    @Autowired
    private TytSequenceRpcService tytSequenceRpcService;


    @Test
    void getSequence() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 10; i++) {
            executorService.execute(() -> {
                String tytWaybillNbr = tytSequenceRpcService.getSequence("tyt_waybill_nbr");
                System.out.println(tytWaybillNbr);
            });
        }

        String tytWaybillNbr1 = tytSequenceRpcService.getSequence("tyt_waybill_nbr");
        System.out.println(tytWaybillNbr1);
        String tytWaybillNbr2 = tytSequenceRpcService.getSequence("tyt_waybill_nbr");
        System.out.println(tytWaybillNbr2);
        String tytWaybillNbr3 = tytSequenceRpcService.getSequence("tyt_waybill_nbr");
        System.out.println(tytWaybillNbr3);

        Thread.sleep(10000);

    }
}
