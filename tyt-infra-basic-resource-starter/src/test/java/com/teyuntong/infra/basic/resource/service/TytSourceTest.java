package com.teyuntong.infra.basic.resource.service;

import com.teyuntong.infra.basic.resource.TestBase;
import com.teyuntong.infra.basic.resource.client.source.service.TytSourceRpcService;
import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * 字典
 *
 * <AUTHOR>
 * @since 2025-04-09 13:56
 */
@Slf4j
@Disabled
public class TytSourceTest extends TestBase {
    @Resource
    private TytSourceRpcService tytSourceRpcService;

    @Test
    void getByGroupCodeAndValue() {
        TytSourceVO userClass = tytSourceRpcService.getByGroupCodeAndValue("user_class", "2");
        log.info("{}", userClass);
    }

    @Test
    void getByGroupCodeValueSubValue() {
        TytSourceVO userClass = tytSourceRpcService.getByGroupCodeValueSubValue("user_class", "2", "6");
        log.info("{}", userClass);
    }
}
