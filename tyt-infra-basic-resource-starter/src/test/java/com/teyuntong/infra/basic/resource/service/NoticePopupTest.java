package com.teyuntong.infra.basic.resource.service;

import cn.hutool.json.JSONUtil;
import com.teyuntong.infra.basic.resource.TestBase;
import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.service.NoticePopupTemplRpcService;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.basic.resource.service.rpc.popup.NoticePopupTemplRpcServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @since 2024/11/15 15:25
 */
@Slf4j
@RequiredArgsConstructor
@Disabled
public class NoticePopupTest extends TestBase {

    @Autowired
    private  NoticePopupTemplRpcServiceImpl noticePopupTemplRpcService;

    @Test
    void getByTypeTest() throws Exception {

        NoticePopupTemplVo byType = noticePopupTemplRpcService.getByType(PopupTypeEnum.专车好货提醒);
        log.info(JSONUtil.toJsonStr(byType));

//        mockMvc.perform(post("/rpc/popup/temple/getByType")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .header("x-app-client-sign", "123")
//                        .header("x-app-client-version", "1.0")
//                        .header("x-app-client-id", "1002001059")
//                        .header("x-app-os-version", "1234")
//                        .content(JSONUtil.toJsonStr(PopupTypeEnum.会员升级)))
//                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
//                .andExpect(status().isOk());
//        Assertions.assertTrue(true);
    }
}
