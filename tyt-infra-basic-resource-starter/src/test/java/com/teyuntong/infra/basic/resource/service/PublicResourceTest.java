package com.teyuntong.infra.basic.resource.service;

import cn.hutool.json.JSONUtil;
import com.teyuntong.infra.basic.resource.TestBase;
import com.teyuntong.infra.basic.resource.client.pubresource.service.PublicResourceRpcService;
import com.teyuntong.infra.basic.resource.client.pubresource.vo.PublicResourceVO;
import com.teyuntong.infra.basic.resource.client.tytconfig.service.ConfigRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.Get;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @since 2024/11/15 15:25
 */
@Slf4j
@RequiredArgsConstructor
@Disabled
public class PublicResourceTest extends TestBase {

    @Autowired
    private PublicResourceRpcService publicResourceRpcService;

    @Test
    void getByNameTest() {

        List<String> list = Arrays.asList("invoice_transport_publish_tab_word", "often_route_new_old_switch");
//        List<PublicResourceVO> byNameList = publicResourceRpcService.getByNameList(list);
        List<PublicResourceVO> byNameList = publicResourceRpcService.getAll();
        System.out.print(JSONUtil.toJsonStr(byNameList));

    }

    @Test
    void getByNameTest1() throws Exception {
        String paramNames = "invoice_transport_publish_tab_word, often_route_new_old_switch";

        mockMvc.perform(post("/public/resource/getByNameList")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .param("paramNames", paramNames))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                        .andExpect(status().isOk());
        Assertions.assertTrue(true);

    }
}
